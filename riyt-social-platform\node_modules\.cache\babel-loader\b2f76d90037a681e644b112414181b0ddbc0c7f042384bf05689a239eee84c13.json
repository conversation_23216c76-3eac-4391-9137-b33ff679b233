{"ast": null, "code": "import none from \"./none.js\";\nexport default function (series) {\n  var sums = series.map(sum);\n  return none(series).sort(function (a, b) {\n    return sums[a] - sums[b];\n  });\n}\nexport function sum(series) {\n  var s = 0,\n    i = -1,\n    n = series.length,\n    v;\n  while (++i < n) if (v = +series[i][1]) s += v;\n  return s;\n}", "map": {"version": 3, "names": ["none", "series", "sums", "map", "sum", "sort", "a", "b", "s", "i", "n", "length", "v"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-shape/src/order/ascending.js"], "sourcesContent": ["import none from \"./none.js\";\n\nexport default function(series) {\n  var sums = series.map(sum);\n  return none(series).sort(function(a, b) { return sums[a] - sums[b]; });\n}\n\nexport function sum(series) {\n  var s = 0, i = -1, n = series.length, v;\n  while (++i < n) if (v = +series[i][1]) s += v;\n  return s;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAGD,MAAM,CAACE,GAAG,CAACC,GAAG,CAAC;EAC1B,OAAOJ,IAAI,CAACC,MAAM,CAAC,CAACI,IAAI,CAAC,UAASC,CAAC,EAAEC,CAAC,EAAE;IAAE,OAAOL,IAAI,CAACI,CAAC,CAAC,GAAGJ,IAAI,CAACK,CAAC,CAAC;EAAE,CAAC,CAAC;AACxE;AAEA,OAAO,SAASH,GAAGA,CAACH,MAAM,EAAE;EAC1B,IAAIO,CAAC,GAAG,CAAC;IAAEC,CAAC,GAAG,CAAC,CAAC;IAAEC,CAAC,GAAGT,MAAM,CAACU,MAAM;IAAEC,CAAC;EACvC,OAAO,EAAEH,CAAC,GAAGC,CAAC,EAAE,IAAIE,CAAC,GAAG,CAACX,MAAM,CAACQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,IAAII,CAAC;EAC7C,OAAOJ,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}