import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Avatar,
  Chip,

} from '@mui/material';
import {
  TrendingUp,
  Schedule,
  Create,
  Analytics,
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
} from '@mui/icons-material';
// import { useAuth } from '../contexts/AuthContext';

const DashboardPage: React.FC = () => {
  // const { user } = useAuth();

  // Demo user for testing
  const user = {
    displayName: 'Demo User',
    email: '<EMAIL>',
  };

  const stats = [
    {
      title: 'Posts This Month',
      value: '24',
      change: '+12%',
      icon: <Create />,
      color: 'primary',
    },
    {
      title: 'Scheduled Posts',
      value: '8',
      change: '+3',
      icon: <Schedule />,
      color: 'warning',
    },
    {
      title: 'Total Engagement',
      value: '1.2K',
      change: '+18%',
      icon: <TrendingUp />,
      color: 'success',
    },
    {
      title: 'Reach',
      value: '5.4K',
      change: '+25%',
      icon: <Analytics />,
      color: 'info',
    },
  ];

  const connectedAccounts = [
    { platform: 'Facebook', icon: <Facebook />, connected: true, posts: 12 },
    { platform: 'Twitter', icon: <Twitter />, connected: true, posts: 8 },
    { platform: 'Instagram', icon: <Instagram />, connected: false, posts: 0 },
    { platform: 'LinkedIn', icon: <LinkedIn />, connected: true, posts: 4 },
  ];

  const recentPosts = [
    {
      id: 1,
      content: 'Excited to share our latest product update! 🚀',
      platforms: ['Facebook', 'Twitter'],
      status: 'Published',
      engagement: 45,
      date: '2 hours ago',
    },
    {
      id: 2,
      content: 'Behind the scenes of our team meeting today...',
      platforms: ['LinkedIn'],
      status: 'Scheduled',
      engagement: 0,
      date: 'Tomorrow at 9:00 AM',
    },
    {
      id: 3,
      content: 'Check out this amazing customer testimonial! ⭐',
      platforms: ['Facebook', 'Instagram'],
      status: 'Published',
      engagement: 78,
      date: '1 day ago',
    },
  ];

  return (
    <Box>
      {/* Welcome Section */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome back, {user?.displayName || 'User'}! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Here's what's happening with your social media accounts today.
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      {stat.title}
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color={`${stat.color}.main`}>
                      {stat.change} from last month
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: `${stat.color}.main` }}>
                    {stat.icon}
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Connected Accounts */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Connected Accounts
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                {connectedAccounts.map((account, index) => (
                  <Box
                    key={index}
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                    p={2}
                    border={1}
                    borderColor="divider"
                    borderRadius={1}
                  >
                    <Box display="flex" alignItems="center" gap={2}>
                      <Avatar sx={{ bgcolor: account.connected ? 'success.main' : 'grey.400' }}>
                        {account.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="body1">{account.platform}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {account.posts} posts this month
                        </Typography>
                      </Box>
                    </Box>
                    <Chip
                      label={account.connected ? 'Connected' : 'Connect'}
                      color={account.connected ? 'success' : 'default'}
                      variant={account.connected ? 'filled' : 'outlined'}
                    />
                  </Box>
                ))}
              </Box>
              <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                Manage Accounts
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Posts */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Posts
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                {recentPosts.map((post) => (
                  <Box
                    key={post.id}
                    p={2}
                    border={1}
                    borderColor="divider"
                    borderRadius={1}
                  >
                    <Typography variant="body2" gutterBottom>
                      {post.content}
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      {post.platforms.map((platform) => (
                        <Chip key={platform} label={platform} size="small" />
                      ))}
                    </Box>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="caption" color="text.secondary">
                        {post.date}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Chip
                          label={post.status}
                          size="small"
                          color={post.status === 'Published' ? 'success' : 'warning'}
                        />
                        {post.status === 'Published' && (
                          <Typography variant="caption" color="text.secondary">
                            {post.engagement} engagements
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
              <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                View All Posts
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button variant="contained" fullWidth startIcon={<Create />}>
                    Create Post
                  </Button>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button variant="outlined" fullWidth startIcon={<Schedule />}>
                    Schedule Post
                  </Button>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button variant="outlined" fullWidth startIcon={<Analytics />}>
                    View Analytics
                  </Button>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button variant="outlined" fullWidth>
                    Connect Account
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
