{"ast": null, "code": "export var combineCoordinateForDefaultIndex = (width, height, layout, offset, tooltipTicks, defaultIndex, tooltipConfigurations, tooltipPayloadSearcher) => {\n  if (defaultIndex == null || tooltipPayloadSearcher == null) {\n    return undefined;\n  }\n  // With defaultIndex alone, we don't have enough information to decide _which_ of the multiple tooltips to display. So we choose the first one.\n  var firstConfiguration = tooltipConfigurations[0];\n  // @ts-expect-error we need to rethink the tooltipPayloadSearcher type\n  var maybePosition = firstConfiguration == null ? undefined : tooltipPayloadSearcher(firstConfiguration.positions, defaultIndex);\n  if (maybePosition != null) {\n    return maybePosition;\n  }\n  var tick = tooltipTicks === null || tooltipTicks === void 0 ? void 0 : tooltipTicks[Number(defaultIndex)];\n  if (!tick) {\n    return undefined;\n  }\n  switch (layout) {\n    case 'horizontal':\n      {\n        return {\n          x: tick.coordinate,\n          y: (offset.top + height) / 2\n        };\n      }\n    default:\n      {\n        // This logic is not super sound - it conflates vertical, radial, centric layouts into just one. TODO improve!\n        return {\n          x: (offset.left + width) / 2,\n          y: tick.coordinate\n        };\n      }\n  }\n};", "map": {"version": 3, "names": ["combineCoordinateForDefaultIndex", "width", "height", "layout", "offset", "tooltipTicks", "defaultIndex", "tooltipConfigurations", "tooltipPayloadSearcher", "undefined", "firstConfiguration", "maybePosition", "positions", "tick", "Number", "x", "coordinate", "y", "top", "left"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/state/selectors/combiners/combineCoordinateForDefaultIndex.js"], "sourcesContent": ["export var combineCoordinateForDefaultIndex = (width, height, layout, offset, tooltipTicks, defaultIndex, tooltipConfigurations, tooltipPayloadSearcher) => {\n  if (defaultIndex == null || tooltipPayloadSearcher == null) {\n    return undefined;\n  }\n  // With defaultIndex alone, we don't have enough information to decide _which_ of the multiple tooltips to display. So we choose the first one.\n  var firstConfiguration = tooltipConfigurations[0];\n  // @ts-expect-error we need to rethink the tooltipPayloadSearcher type\n  var maybePosition = firstConfiguration == null ? undefined : tooltipPayloadSearcher(firstConfiguration.positions, defaultIndex);\n  if (maybePosition != null) {\n    return maybePosition;\n  }\n  var tick = tooltipTicks === null || tooltipTicks === void 0 ? void 0 : tooltipTicks[Number(defaultIndex)];\n  if (!tick) {\n    return undefined;\n  }\n  switch (layout) {\n    case 'horizontal':\n      {\n        return {\n          x: tick.coordinate,\n          y: (offset.top + height) / 2\n        };\n      }\n    default:\n      {\n        // This logic is not super sound - it conflates vertical, radial, centric layouts into just one. TODO improve!\n        return {\n          x: (offset.left + width) / 2,\n          y: tick.coordinate\n        };\n      }\n  }\n};"], "mappings": "AAAA,OAAO,IAAIA,gCAAgC,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,sBAAsB,KAAK;EAC1J,IAAIF,YAAY,IAAI,IAAI,IAAIE,sBAAsB,IAAI,IAAI,EAAE;IAC1D,OAAOC,SAAS;EAClB;EACA;EACA,IAAIC,kBAAkB,GAAGH,qBAAqB,CAAC,CAAC,CAAC;EACjD;EACA,IAAII,aAAa,GAAGD,kBAAkB,IAAI,IAAI,GAAGD,SAAS,GAAGD,sBAAsB,CAACE,kBAAkB,CAACE,SAAS,EAAEN,YAAY,CAAC;EAC/H,IAAIK,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOA,aAAa;EACtB;EACA,IAAIE,IAAI,GAAGR,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACS,MAAM,CAACR,YAAY,CAAC,CAAC;EACzG,IAAI,CAACO,IAAI,EAAE;IACT,OAAOJ,SAAS;EAClB;EACA,QAAQN,MAAM;IACZ,KAAK,YAAY;MACf;QACE,OAAO;UACLY,CAAC,EAAEF,IAAI,CAACG,UAAU;UAClBC,CAAC,EAAE,CAACb,MAAM,CAACc,GAAG,GAAGhB,MAAM,IAAI;QAC7B,CAAC;MACH;IACF;MACE;QACE;QACA,OAAO;UACLa,CAAC,EAAE,CAACX,MAAM,CAACe,IAAI,GAAGlB,KAAK,IAAI,CAAC;UAC5BgB,CAAC,EAAEJ,IAAI,CAACG;QACV,CAAC;MACH;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}