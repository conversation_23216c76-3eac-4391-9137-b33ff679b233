{"ast": null, "code": "import { cubehelix as colorCubehelix } from \"d3-color\";\nimport color, { hue } from \"./color.js\";\nfunction cubehelix(hue) {\n  return function cubehelixGamma(y) {\n    y = +y;\n    function cubehelix(start, end) {\n      var h = hue((start = colorCubehelix(start)).h, (end = colorCubehelix(end)).h),\n        s = color(start.s, end.s),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n      return function (t) {\n        start.h = h(t);\n        start.s = s(t);\n        start.l = l(Math.pow(t, y));\n        start.opacity = opacity(t);\n        return start + \"\";\n      };\n    }\n    cubehelix.gamma = cubehelixGamma;\n    return cubehelix;\n  }(1);\n}\nexport default cubehelix(hue);\nexport var cubehelixLong = cubehelix(color);", "map": {"version": 3, "names": ["cubehelix", "colorCubehelix", "color", "hue", "cubehelix<PERSON>ma", "y", "start", "end", "h", "s", "l", "opacity", "t", "Math", "pow", "gamma", "cubehelixLong"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-interpolate/src/cubehelix.js"], "sourcesContent": ["import {cubehelix as colorCubehelix} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction cubehelix(hue) {\n  return (function cubehelixGamma(y) {\n    y = +y;\n\n    function cubehelix(start, end) {\n      var h = hue((start = colorCubehelix(start)).h, (end = colorCubehelix(end)).h),\n          s = color(start.s, end.s),\n          l = color(start.l, end.l),\n          opacity = color(start.opacity, end.opacity);\n      return function(t) {\n        start.h = h(t);\n        start.s = s(t);\n        start.l = l(Math.pow(t, y));\n        start.opacity = opacity(t);\n        return start + \"\";\n      };\n    }\n\n    cubehelix.gamma = cubehelixGamma;\n\n    return cubehelix;\n  })(1);\n}\n\nexport default cubehelix(hue);\nexport var cubehelixLong = cubehelix(color);\n"], "mappings": "AAAA,SAAQA,SAAS,IAAIC,cAAc,QAAO,UAAU;AACpD,OAAOC,KAAK,IAAGC,GAAG,QAAO,YAAY;AAErC,SAASH,SAASA,CAACG,GAAG,EAAE;EACtB,OAAQ,SAASC,cAAcA,CAACC,CAAC,EAAE;IACjCA,CAAC,GAAG,CAACA,CAAC;IAEN,SAASL,SAASA,CAACM,KAAK,EAAEC,GAAG,EAAE;MAC7B,IAAIC,CAAC,GAAGL,GAAG,CAAC,CAACG,KAAK,GAAGL,cAAc,CAACK,KAAK,CAAC,EAAEE,CAAC,EAAE,CAACD,GAAG,GAAGN,cAAc,CAACM,GAAG,CAAC,EAAEC,CAAC,CAAC;QACzEC,CAAC,GAAGP,KAAK,CAACI,KAAK,CAACG,CAAC,EAAEF,GAAG,CAACE,CAAC,CAAC;QACzBC,CAAC,GAAGR,KAAK,CAACI,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACG,CAAC,CAAC;QACzBC,OAAO,GAAGT,KAAK,CAACI,KAAK,CAACK,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAAC;MAC/C,OAAO,UAASC,CAAC,EAAE;QACjBN,KAAK,CAACE,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;QACdN,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;QACdN,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACG,IAAI,CAACC,GAAG,CAACF,CAAC,EAAEP,CAAC,CAAC,CAAC;QAC3BC,KAAK,CAACK,OAAO,GAAGA,OAAO,CAACC,CAAC,CAAC;QAC1B,OAAON,KAAK,GAAG,EAAE;MACnB,CAAC;IACH;IAEAN,SAAS,CAACe,KAAK,GAAGX,cAAc;IAEhC,OAAOJ,SAAS;EAClB,CAAC,CAAE,CAAC,CAAC;AACP;AAEA,eAAeA,SAAS,CAACG,GAAG,CAAC;AAC7B,OAAO,IAAIa,aAAa,GAAGhB,SAAS,CAACE,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}