{"ast": null, "code": "import { InternSet } from \"internmap\";\nexport default function disjoint(values, other) {\n  const iterator = other[Symbol.iterator](),\n    set = new InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while ({\n      value,\n      done\n    } = iterator.next()) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["InternSet", "disjoint", "values", "other", "iterator", "Symbol", "set", "v", "has", "value", "done", "next", "Object", "is", "add"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-array/src/disjoint.js"], "sourcesContent": ["import {InternSet} from \"internmap\";\n\nexport default function disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC9C,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;IAAEE,GAAG,GAAG,IAAIN,SAAS,CAAC,CAAC;EAChE,KAAK,MAAMO,CAAC,IAAIL,MAAM,EAAE;IACtB,IAAII,GAAG,CAACE,GAAG,CAACD,CAAC,CAAC,EAAE,OAAO,KAAK;IAC5B,IAAIE,KAAK,EAAEC,IAAI;IACf,OAAQ;MAACD,KAAK;MAAEC;IAAI,CAAC,GAAGN,QAAQ,CAACO,IAAI,CAAC,CAAC,EAAG;MACxC,IAAID,IAAI,EAAE;MACV,IAAIE,MAAM,CAACC,EAAE,CAACN,CAAC,EAAEE,KAAK,CAAC,EAAE,OAAO,KAAK;MACrCH,GAAG,CAACQ,GAAG,CAACL,KAAK,CAAC;IAChB;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}