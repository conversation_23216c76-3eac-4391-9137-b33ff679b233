{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst minBy$1 = require('../../array/minBy.js');\nconst identity = require('../../function/identity.js');\nconst iteratee = require('../util/iteratee.js');\nfunction minBy(items, iteratee$1) {\n  if (items == null) {\n    return undefined;\n  }\n  return minBy$1.minBy(Array.from(items), iteratee.iteratee(iteratee$1 ?? identity.identity));\n}\nexports.minBy = minBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "minBy$1", "require", "identity", "iteratee", "minBy", "items", "iteratee$1", "undefined", "Array", "from"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/es-toolkit/dist/compat/math/minBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst minBy$1 = require('../../array/minBy.js');\nconst identity = require('../../function/identity.js');\nconst iteratee = require('../util/iteratee.js');\n\nfunction minBy(items, iteratee$1) {\n    if (items == null) {\n        return undefined;\n    }\n    return minBy$1.minBy(Array.from(items), iteratee.iteratee(iteratee$1 ?? identity.identity));\n}\n\nexports.minBy = minBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC/C,MAAMC,QAAQ,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AACtD,MAAME,QAAQ,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAE/C,SAASG,KAAKA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC9B,IAAID,KAAK,IAAI,IAAI,EAAE;IACf,OAAOE,SAAS;EACpB;EACA,OAAOP,OAAO,CAACI,KAAK,CAACI,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,EAAEF,QAAQ,CAACA,QAAQ,CAACG,UAAU,IAAIJ,QAAQ,CAACA,QAAQ,CAAC,CAAC;AAC/F;AAEAN,OAAO,CAACQ,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}