[{"C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\ThemeContext.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\ErrorBoundary.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\styles\\theme.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\auth.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\config.ts": "9"}, {"size": 554, "mtime": 1752921485732, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1752921485219, "results": "12", "hashOfConfig": "11"}, {"size": 1763, "mtime": 1752922264343, "results": "13", "hashOfConfig": "11"}, {"size": 4660, "mtime": 1752922451837, "results": "14", "hashOfConfig": "11"}, {"size": 2415, "mtime": 1752922278447, "results": "15", "hashOfConfig": "11"}, {"size": 3013, "mtime": 1752922019754, "results": "16", "hashOfConfig": "11"}, {"size": 3905, "mtime": 1752921981101, "results": "17", "hashOfConfig": "11"}, {"size": 5043, "mtime": 1752922392759, "results": "18", "hashOfConfig": "11"}, {"size": 1030, "mtime": 1752921824791, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13hkmlg", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\styles\\theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\config.ts", [], []]