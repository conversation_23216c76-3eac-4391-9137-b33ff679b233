[{"C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\ThemeContext.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\ErrorBoundary.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\styles\\theme.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\auth.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\config.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\DashboardPage.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\AuthPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\EmailVerificationPage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\LoadingSpinner.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ProtectedRoute.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\DashboardLayout.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\LoginForm.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\RegisterForm.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ForgotPasswordForm.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Header.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Sidebar.tsx": "20"}, {"size": 554, "mtime": 1752921485732, "results": "21", "hashOfConfig": "22"}, {"size": 425, "mtime": 1752921485219, "results": "23", "hashOfConfig": "22"}, {"size": 3694, "mtime": 1752924758942, "results": "24", "hashOfConfig": "22"}, {"size": 4660, "mtime": 1752922451837, "results": "25", "hashOfConfig": "22"}, {"size": 2415, "mtime": 1752922278447, "results": "26", "hashOfConfig": "22"}, {"size": 3013, "mtime": 1752922019754, "results": "27", "hashOfConfig": "22"}, {"size": 3905, "mtime": 1752921981101, "results": "28", "hashOfConfig": "22"}, {"size": 5264, "mtime": 1752924351125, "results": "29", "hashOfConfig": "22"}, {"size": 1387, "mtime": 1752924325259, "results": "30", "hashOfConfig": "22"}, {"size": 8616, "mtime": 1752924444249, "results": "31", "hashOfConfig": "22"}, {"size": 2370, "mtime": 1752922972964, "results": "32", "hashOfConfig": "22"}, {"size": 4814, "mtime": 1752923012154, "results": "33", "hashOfConfig": "22"}, {"size": 1185, "mtime": 1752922005330, "results": "34", "hashOfConfig": "22"}, {"size": 1054, "mtime": 1752922990742, "results": "35", "hashOfConfig": "22"}, {"size": 1239, "mtime": 1752923093524, "results": "36", "hashOfConfig": "22"}, {"size": 5848, "mtime": 1752922654537, "results": "37", "hashOfConfig": "22"}, {"size": 9000, "mtime": 1752922687593, "results": "38", "hashOfConfig": "22"}, {"size": 4541, "mtime": 1752922956717, "results": "39", "hashOfConfig": "22"}, {"size": 5305, "mtime": 1752924419974, "results": "40", "hashOfConfig": "22"}, {"size": 6036, "mtime": 1752923069472, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13hkmlg", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\App.tsx", ["102"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\styles\\theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\auth.ts", ["103"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\AuthPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\EmailVerificationPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ForgotPasswordForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Sidebar.tsx", [], [], {"ruleId": "104", "severity": 1, "message": "105", "line": 12, "column": 7, "nodeType": "106", "messageId": "107", "endLine": 12, "endColumn": 15}, {"ruleId": "108", "severity": 2, "message": "109", "line": 19, "column": 1, "nodeType": "110", "endLine": 19, "endColumn": 36, "fix": "111"}, "@typescript-eslint/no-unused-vars", "'demoUser' is assigned a value but never used.", "Identifier", "unusedVar", "import/first", "Import in body of module; reorder to top.", "ImportDeclaration", {"range": "112", "text": "113"}, [0, 475], "import {\n  createUserWithEmailAndPassword,\n  signInWithEmailAndPassword,\n  signOut,\n  sendPasswordResetEmail,\n  sendEmailVerification,\n  updateProfile,\n  User as FirebaseUser,\n  GoogleAuthProvider,\n  signInWithPopup,\n  onAuthStateChanged,\n  NextOrObserver,\n} from 'firebase/auth';\nimport { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';\nimport { auth, db } from './config';\nimport { User } from '../../types';\n\n// Demo mode check\nconst isDemoMode = !auth || !db;"]