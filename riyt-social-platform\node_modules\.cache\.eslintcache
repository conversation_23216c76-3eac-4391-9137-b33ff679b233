[{"C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\ThemeContext.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\ErrorBoundary.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\styles\\theme.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\auth.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\config.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\DashboardPage.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\AuthPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\EmailVerificationPage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\LoadingSpinner.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ProtectedRoute.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\DashboardLayout.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\LoginForm.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\RegisterForm.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ForgotPasswordForm.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Header.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Sidebar.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\OAuthCallbackPage.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\SocialAccountsPage.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\CreatePostPage.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\types\\index.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\index.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\facebook.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\twitter.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\ContentCalendarPage.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\MediaLibraryPage.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\CreatePostPageEnhanced.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\ScheduledPostsPage.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\ImageEditor\\ImageEditor.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\scheduling\\index.ts": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\AnalyticsPage.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\analytics\\index.ts": "35"}, {"size": 554, "mtime": 1752921485732, "results": "36", "hashOfConfig": "37"}, {"size": 425, "mtime": 1752921485219, "results": "38", "hashOfConfig": "37"}, {"size": 3885, "mtime": 1752928460782, "results": "39", "hashOfConfig": "37"}, {"size": 4660, "mtime": 1752922451837, "results": "40", "hashOfConfig": "37"}, {"size": 2415, "mtime": 1752922278447, "results": "41", "hashOfConfig": "37"}, {"size": 3013, "mtime": 1752922019754, "results": "42", "hashOfConfig": "37"}, {"size": 3905, "mtime": 1752921981101, "results": "43", "hashOfConfig": "37"}, {"size": 5264, "mtime": 1752924351125, "results": "44", "hashOfConfig": "37"}, {"size": 1387, "mtime": 1752924325259, "results": "45", "hashOfConfig": "37"}, {"size": 8664, "mtime": 1752927834186, "results": "46", "hashOfConfig": "37"}, {"size": 2370, "mtime": 1752922972964, "results": "47", "hashOfConfig": "37"}, {"size": 4814, "mtime": 1752923012154, "results": "48", "hashOfConfig": "37"}, {"size": 1185, "mtime": 1752922005330, "results": "49", "hashOfConfig": "37"}, {"size": 1054, "mtime": 1752922990742, "results": "50", "hashOfConfig": "37"}, {"size": 1239, "mtime": 1752923093524, "results": "51", "hashOfConfig": "37"}, {"size": 5848, "mtime": 1752922654537, "results": "52", "hashOfConfig": "37"}, {"size": 9000, "mtime": 1752922687593, "results": "53", "hashOfConfig": "37"}, {"size": 4541, "mtime": 1752922956717, "results": "54", "hashOfConfig": "37"}, {"size": 5305, "mtime": 1752924419974, "results": "55", "hashOfConfig": "37"}, {"size": 6036, "mtime": 1752923069472, "results": "56", "hashOfConfig": "37"}, {"size": 6446, "mtime": 1752925156322, "results": "57", "hashOfConfig": "37"}, {"size": 13390, "mtime": 1752925454706, "results": "58", "hashOfConfig": "37"}, {"size": 11678, "mtime": 1752925488847, "results": "59", "hashOfConfig": "37"}, {"size": 1750, "mtime": 1752921816227, "results": "60", "hashOfConfig": "37"}, {"size": 9367, "mtime": 1752925375643, "results": "61", "hashOfConfig": "37"}, {"size": 7150, "mtime": 1752925329331, "results": "62", "hashOfConfig": "37"}, {"size": 8688, "mtime": 1752925361557, "results": "63", "hashOfConfig": "37"}, {"size": 15492, "mtime": 1752926520226, "results": "64", "hashOfConfig": "37"}, {"size": 15626, "mtime": 1752926379254, "results": "65", "hashOfConfig": "37"}, {"size": 23778, "mtime": 1752928499265, "results": "66", "hashOfConfig": "37"}, {"size": 17963, "mtime": 1752927917039, "results": "67", "hashOfConfig": "37"}, {"size": 13449, "mtime": 1752926319173, "results": "68", "hashOfConfig": "37"}, {"size": 10275, "mtime": 1752925996191, "results": "69", "hashOfConfig": "37"}, {"size": 18528, "mtime": 1752928294275, "results": "70", "hashOfConfig": "37"}, {"size": 10888, "mtime": 1752928230321, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13hkmlg", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\styles\\theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\auth.ts", ["177"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\AuthPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\EmailVerificationPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ForgotPasswordForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\OAuthCallbackPage.tsx", ["178"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\SocialAccountsPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\CreatePostPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\index.ts", ["179"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\facebook.ts", ["180"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\twitter.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\ContentCalendarPage.tsx", ["181", "182", "183"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\MediaLibraryPage.tsx", ["184", "185"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\CreatePostPageEnhanced.tsx", ["186"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\ScheduledPostsPage.tsx", ["187", "188", "189", "190", "191", "192"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\ImageEditor\\ImageEditor.tsx", ["193", "194", "195", "196", "197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\scheduling\\index.ts", ["208"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\AnalyticsPage.tsx", ["209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\analytics\\index.ts", ["226", "227", "228", "229"], [], {"ruleId": "230", "severity": 2, "message": "231", "line": 19, "column": 1, "nodeType": "232", "endLine": 19, "endColumn": 36, "fix": "233"}, {"ruleId": "234", "severity": 1, "message": "235", "line": 32, "column": 6, "nodeType": "236", "endLine": 32, "endColumn": 30, "suggestions": "237"}, {"ruleId": "238", "severity": 1, "message": "239", "line": 1, "column": 41, "nodeType": "240", "messageId": "241", "endLine": 1, "endColumn": 45}, {"ruleId": "238", "severity": 1, "message": "239", "line": 1, "column": 41, "nodeType": "240", "messageId": "241", "endLine": 1, "endColumn": 45}, {"ruleId": "238", "severity": 1, "message": "242", "line": 1, "column": 38, "nodeType": "240", "messageId": "241", "endLine": 1, "endColumn": 45}, {"ruleId": "238", "severity": 1, "message": "243", "line": 15, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 15, "endColumn": 10}, {"ruleId": "238", "severity": 1, "message": "244", "line": 30, "column": 17, "nodeType": "240", "messageId": "241", "endLine": 30, "endColumn": 27}, {"ruleId": "238", "severity": 1, "message": "244", "line": 32, "column": 17, "nodeType": "240", "messageId": "241", "endLine": 32, "endColumn": 27}, {"ruleId": "238", "severity": 1, "message": "245", "line": 61, "column": 10, "nodeType": "240", "messageId": "241", "endLine": 61, "endColumn": 17}, {"ruleId": "238", "severity": 1, "message": "246", "line": 58, "column": 29, "nodeType": "240", "messageId": "241", "endLine": 58, "endColumn": 44}, {"ruleId": "238", "severity": 1, "message": "247", "line": 12, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 12, "endColumn": 7}, {"ruleId": "238", "severity": 1, "message": "248", "line": 13, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 13, "endColumn": 11}, {"ruleId": "238", "severity": 1, "message": "249", "line": 14, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 14, "endColumn": 17}, {"ruleId": "238", "severity": 1, "message": "250", "line": 15, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 15, "endColumn": 15}, {"ruleId": "238", "severity": 1, "message": "251", "line": 16, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 16, "endColumn": 26}, {"ruleId": "238", "severity": 1, "message": "245", "line": 55, "column": 10, "nodeType": "240", "messageId": "241", "endLine": 55, "endColumn": 17}, {"ruleId": "238", "severity": 1, "message": "252", "line": 11, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 11, "endColumn": 14}, {"ruleId": "238", "severity": 1, "message": "253", "line": 12, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 12, "endColumn": 13}, {"ruleId": "238", "severity": 1, "message": "254", "line": 13, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 13, "endColumn": 9}, {"ruleId": "238", "severity": 1, "message": "255", "line": 14, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 14, "endColumn": 11}, {"ruleId": "238", "severity": 1, "message": "256", "line": 27, "column": 11, "nodeType": "240", "messageId": "241", "endLine": 27, "endColumn": 19}, {"ruleId": "238", "severity": 1, "message": "257", "line": 28, "column": 14, "nodeType": "240", "messageId": "241", "endLine": 28, "endColumn": 25}, {"ruleId": "238", "severity": 1, "message": "244", "line": 29, "column": 20, "nodeType": "240", "messageId": "241", "endLine": 29, "endColumn": 30}, {"ruleId": "238", "severity": 1, "message": "258", "line": 30, "column": 13, "nodeType": "240", "messageId": "241", "endLine": 30, "endColumn": 23}, {"ruleId": "238", "severity": 1, "message": "259", "line": 31, "column": 14, "nodeType": "240", "messageId": "241", "endLine": 31, "endColumn": 25}, {"ruleId": "238", "severity": 1, "message": "260", "line": 53, "column": 10, "nodeType": "240", "messageId": "241", "endLine": 53, "endColumn": 22}, {"ruleId": "238", "severity": 1, "message": "261", "line": 53, "column": 24, "nodeType": "240", "messageId": "241", "endLine": 53, "endColumn": 39}, {"ruleId": "238", "severity": 1, "message": "262", "line": 60, "column": 10, "nodeType": "240", "messageId": "241", "endLine": 60, "endColumn": 20}, {"ruleId": "238", "severity": 1, "message": "263", "line": 60, "column": 22, "nodeType": "240", "messageId": "241", "endLine": 60, "endColumn": 35}, {"ruleId": "238", "severity": 1, "message": "264", "line": 61, "column": 10, "nodeType": "240", "messageId": "241", "endLine": 61, "endColumn": 14}, {"ruleId": "238", "severity": 1, "message": "265", "line": 61, "column": 16, "nodeType": "240", "messageId": "241", "endLine": 61, "endColumn": 23}, {"ruleId": "238", "severity": 1, "message": "266", "line": 1, "column": 17, "nodeType": "240", "messageId": "241", "endLine": 1, "endColumn": 22}, {"ruleId": "238", "severity": 1, "message": "247", "line": 12, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 12, "endColumn": 7}, {"ruleId": "238", "severity": 1, "message": "248", "line": 13, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 13, "endColumn": 11}, {"ruleId": "238", "severity": 1, "message": "249", "line": 14, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 14, "endColumn": 17}, {"ruleId": "238", "severity": 1, "message": "250", "line": 15, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 15, "endColumn": 15}, {"ruleId": "238", "severity": 1, "message": "251", "line": 16, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 16, "endColumn": 26}, {"ruleId": "238", "severity": 1, "message": "267", "line": 17, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 17, "endColumn": 8}, {"ruleId": "238", "severity": 1, "message": "243", "line": 28, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 28, "endColumn": 10}, {"ruleId": "238", "severity": 1, "message": "268", "line": 29, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 29, "endColumn": 13}, {"ruleId": "238", "severity": 1, "message": "269", "line": 37, "column": 12, "nodeType": "240", "messageId": "241", "endLine": 37, "endColumn": 21}, {"ruleId": "238", "severity": 1, "message": "270", "line": 38, "column": 18, "nodeType": "240", "messageId": "241", "endLine": 38, "endColumn": 27}, {"ruleId": "238", "severity": 1, "message": "271", "line": 43, "column": 11, "nodeType": "240", "messageId": "241", "endLine": 43, "endColumn": 19}, {"ruleId": "238", "severity": 1, "message": "272", "line": 48, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 48, "endColumn": 12}, {"ruleId": "238", "severity": 1, "message": "273", "line": 49, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 49, "endColumn": 7}, {"ruleId": "238", "severity": 1, "message": "274", "line": 52, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 52, "endColumn": 11}, {"ruleId": "238", "severity": 1, "message": "275", "line": 53, "column": 3, "nodeType": "240", "messageId": "241", "endLine": 53, "endColumn": 6}, {"ruleId": "234", "severity": 1, "message": "276", "line": 77, "column": 6, "nodeType": "236", "endLine": 77, "endColumn": 17, "suggestions": "277"}, {"ruleId": "238", "severity": 1, "message": "278", "line": 139, "column": 9, "nodeType": "240", "messageId": "241", "endLine": 139, "endColumn": 24}, {"ruleId": "238", "severity": 1, "message": "239", "line": 1, "column": 26, "nodeType": "240", "messageId": "241", "endLine": 1, "endColumn": 30}, {"ruleId": "238", "severity": 1, "message": "279", "line": 1, "column": 32, "nodeType": "240", "messageId": "241", "endLine": 1, "endColumn": 42}, {"ruleId": "238", "severity": 1, "message": "280", "line": 2, "column": 27, "nodeType": "240", "messageId": "241", "endLine": 2, "endColumn": 37}, {"ruleId": "238", "severity": 1, "message": "281", "line": 2, "column": 39, "nodeType": "240", "messageId": "241", "endLine": 2, "endColumn": 47}, "import/first", "Import in body of module; reorder to top.", "ImportDeclaration", {"range": "282", "text": "283"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleOAuthCallback'. Either include it or remove the dependency array.", "ArrayExpression", ["284"], "@typescript-eslint/no-unused-vars", "'Post' is defined but never used.", "Identifier", "unusedVar", "'useMemo' is defined but never used.", "'Tooltip' is defined but never used.", "'FilterIcon' is defined but never used.", "'loading' is assigned a value but never used.", "'ScheduleOptions' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemAvatar' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'CropIcon' is defined but never used.", "'PaletteIcon' is defined but never used.", "'ZoomInIcon' is defined but never used.", "'ZoomOutIcon' is defined but never used.", "'selectedTool' is assigned a value but never used.", "'setSelectedTool' is assigned a value but never used.", "'saturation' is assigned a value but never used.", "'setSaturation' is assigned a value but never used.", "'zoom' is assigned a value but never used.", "'setZoom' is assigned a value but never used.", "'Dayjs' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'ShareIcon' is defined but never used.", "'MoneyIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAnalyticsData'. Either include it or remove the dependency array.", ["285"], "'recommendations' is assigned a value but never used.", "'PostStatus' is defined but never used.", "'startOfDay' is defined but never used.", "'endOfDay' is defined but never used.", [0, 475], "import {\n  createUserWithEmailAndPassword,\n  signInWithEmailAndPassword,\n  signOut,\n  sendPasswordResetEmail,\n  sendEmailVerification,\n  updateProfile,\n  User as FirebaseUser,\n  GoogleAuthProvider,\n  signInWithPopup,\n  onAuthStateChanged,\n  NextOrObserver,\n} from 'firebase/auth';\nimport { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';\nimport { auth, db } from './config';\nimport { User } from '../../types';\n\n// Demo mode check\nconst isDemoMode = !auth || !db;", {"desc": "286", "fix": "287"}, {"desc": "288", "fix": "289"}, "Update the dependencies array to be: [handleOAuthCallback, platform, searchParams]", {"range": "290", "text": "291"}, "Update the dependencies array to be: [loadAnalyticsData, timeRange]", {"range": "292", "text": "293"}, [979, 1003], "[handleOAuthCallback, platform, searchParams]", [1657, 1668], "[loadAnalyticsData, timeRange]"]