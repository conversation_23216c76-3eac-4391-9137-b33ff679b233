[{"C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\ThemeContext.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\ErrorBoundary.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\styles\\theme.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\auth.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\config.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\DashboardPage.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\AuthPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\EmailVerificationPage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\LoadingSpinner.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ProtectedRoute.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\DashboardLayout.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\LoginForm.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\RegisterForm.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ForgotPasswordForm.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Header.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Sidebar.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\OAuthCallbackPage.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\SocialAccountsPage.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\CreatePostPage.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\types\\index.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\index.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\facebook.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\twitter.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\ContentCalendarPage.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\MediaLibraryPage.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\CreatePostPageEnhanced.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\ScheduledPostsPage.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\ImageEditor\\ImageEditor.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\scheduling\\index.ts": "33"}, {"size": 554, "mtime": 1752921485732, "results": "34", "hashOfConfig": "35"}, {"size": 425, "mtime": 1752921485219, "results": "36", "hashOfConfig": "35"}, {"size": 3856, "mtime": 1752926540147, "results": "37", "hashOfConfig": "35"}, {"size": 4660, "mtime": 1752922451837, "results": "38", "hashOfConfig": "35"}, {"size": 2415, "mtime": 1752922278447, "results": "39", "hashOfConfig": "35"}, {"size": 3013, "mtime": 1752922019754, "results": "40", "hashOfConfig": "35"}, {"size": 3905, "mtime": 1752921981101, "results": "41", "hashOfConfig": "35"}, {"size": 5264, "mtime": 1752924351125, "results": "42", "hashOfConfig": "35"}, {"size": 1387, "mtime": 1752924325259, "results": "43", "hashOfConfig": "35"}, {"size": 8584, "mtime": 1752925440611, "results": "44", "hashOfConfig": "35"}, {"size": 2370, "mtime": 1752922972964, "results": "45", "hashOfConfig": "35"}, {"size": 4814, "mtime": 1752923012154, "results": "46", "hashOfConfig": "35"}, {"size": 1185, "mtime": 1752922005330, "results": "47", "hashOfConfig": "35"}, {"size": 1054, "mtime": 1752922990742, "results": "48", "hashOfConfig": "35"}, {"size": 1239, "mtime": 1752923093524, "results": "49", "hashOfConfig": "35"}, {"size": 5848, "mtime": 1752922654537, "results": "50", "hashOfConfig": "35"}, {"size": 9000, "mtime": 1752922687593, "results": "51", "hashOfConfig": "35"}, {"size": 4541, "mtime": 1752922956717, "results": "52", "hashOfConfig": "35"}, {"size": 5305, "mtime": 1752924419974, "results": "53", "hashOfConfig": "35"}, {"size": 6036, "mtime": 1752923069472, "results": "54", "hashOfConfig": "35"}, {"size": 6446, "mtime": 1752925156322, "results": "55", "hashOfConfig": "35"}, {"size": 13390, "mtime": 1752925454706, "results": "56", "hashOfConfig": "35"}, {"size": 11678, "mtime": 1752925488847, "results": "57", "hashOfConfig": "35"}, {"size": 1750, "mtime": 1752921816227, "results": "58", "hashOfConfig": "35"}, {"size": 9367, "mtime": 1752925375643, "results": "59", "hashOfConfig": "35"}, {"size": 7150, "mtime": 1752925329331, "results": "60", "hashOfConfig": "35"}, {"size": 8688, "mtime": 1752925361557, "results": "61", "hashOfConfig": "35"}, {"size": 15492, "mtime": 1752926520226, "results": "62", "hashOfConfig": "35"}, {"size": 15626, "mtime": 1752926379254, "results": "63", "hashOfConfig": "35"}, {"size": 23626, "mtime": 1752926096733, "results": "64", "hashOfConfig": "35"}, {"size": 17913, "mtime": 1752926185877, "results": "65", "hashOfConfig": "35"}, {"size": 13449, "mtime": 1752926319173, "results": "66", "hashOfConfig": "35"}, {"size": 10275, "mtime": 1752925996191, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13hkmlg", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\styles\\theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\auth.ts", ["167"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\firebase\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\AuthPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\EmailVerificationPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\auth\\ForgotPasswordForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\dashboard\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\OAuthCallbackPage.tsx", ["168"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\SocialAccountsPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\CreatePostPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\index.ts", ["169"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\facebook.ts", ["170"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\social-apis\\twitter.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\ContentCalendarPage.tsx", ["171", "172", "173"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\MediaLibraryPage.tsx", ["174", "175"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\CreatePostPageEnhanced.tsx", ["176"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\pages\\ScheduledPostsPage.tsx", ["177", "178", "179", "180", "181", "182"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\components\\ImageEditor\\ImageEditor.tsx", ["183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\ryt\\riyt-social-platform\\src\\services\\scheduling\\index.ts", ["198"], [], {"ruleId": "199", "severity": 2, "message": "200", "line": 19, "column": 1, "nodeType": "201", "endLine": 19, "endColumn": 36, "fix": "202"}, {"ruleId": "203", "severity": 1, "message": "204", "line": 32, "column": 6, "nodeType": "205", "endLine": 32, "endColumn": 30, "suggestions": "206"}, {"ruleId": "207", "severity": 1, "message": "208", "line": 1, "column": 41, "nodeType": "209", "messageId": "210", "endLine": 1, "endColumn": 45}, {"ruleId": "207", "severity": 1, "message": "208", "line": 1, "column": 41, "nodeType": "209", "messageId": "210", "endLine": 1, "endColumn": 45}, {"ruleId": "207", "severity": 1, "message": "211", "line": 1, "column": 38, "nodeType": "209", "messageId": "210", "endLine": 1, "endColumn": 45}, {"ruleId": "207", "severity": 1, "message": "212", "line": 15, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 15, "endColumn": 10}, {"ruleId": "207", "severity": 1, "message": "213", "line": 30, "column": 17, "nodeType": "209", "messageId": "210", "endLine": 30, "endColumn": 27}, {"ruleId": "207", "severity": 1, "message": "213", "line": 32, "column": 17, "nodeType": "209", "messageId": "210", "endLine": 32, "endColumn": 27}, {"ruleId": "207", "severity": 1, "message": "214", "line": 61, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 61, "endColumn": 17}, {"ruleId": "207", "severity": 1, "message": "215", "line": 58, "column": 29, "nodeType": "209", "messageId": "210", "endLine": 58, "endColumn": 44}, {"ruleId": "207", "severity": 1, "message": "216", "line": 12, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 12, "endColumn": 7}, {"ruleId": "207", "severity": 1, "message": "217", "line": 13, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 13, "endColumn": 11}, {"ruleId": "207", "severity": 1, "message": "218", "line": 14, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 14, "endColumn": 17}, {"ruleId": "207", "severity": 1, "message": "219", "line": 15, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 15, "endColumn": 15}, {"ruleId": "207", "severity": 1, "message": "220", "line": 16, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 16, "endColumn": 26}, {"ruleId": "207", "severity": 1, "message": "214", "line": 55, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 55, "endColumn": 17}, {"ruleId": "207", "severity": 1, "message": "221", "line": 11, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 11, "endColumn": 14}, {"ruleId": "207", "severity": 1, "message": "222", "line": 12, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 12, "endColumn": 13}, {"ruleId": "207", "severity": 1, "message": "223", "line": 13, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 13, "endColumn": 9}, {"ruleId": "207", "severity": 1, "message": "224", "line": 14, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 14, "endColumn": 11}, {"ruleId": "207", "severity": 1, "message": "225", "line": 27, "column": 11, "nodeType": "209", "messageId": "210", "endLine": 27, "endColumn": 19}, {"ruleId": "207", "severity": 1, "message": "226", "line": 28, "column": 14, "nodeType": "209", "messageId": "210", "endLine": 28, "endColumn": 25}, {"ruleId": "207", "severity": 1, "message": "213", "line": 29, "column": 20, "nodeType": "209", "messageId": "210", "endLine": 29, "endColumn": 30}, {"ruleId": "207", "severity": 1, "message": "227", "line": 30, "column": 13, "nodeType": "209", "messageId": "210", "endLine": 30, "endColumn": 23}, {"ruleId": "207", "severity": 1, "message": "228", "line": 31, "column": 14, "nodeType": "209", "messageId": "210", "endLine": 31, "endColumn": 25}, {"ruleId": "207", "severity": 1, "message": "229", "line": 53, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 53, "endColumn": 22}, {"ruleId": "207", "severity": 1, "message": "230", "line": 53, "column": 24, "nodeType": "209", "messageId": "210", "endLine": 53, "endColumn": 39}, {"ruleId": "207", "severity": 1, "message": "231", "line": 60, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 60, "endColumn": 20}, {"ruleId": "207", "severity": 1, "message": "232", "line": 60, "column": 22, "nodeType": "209", "messageId": "210", "endLine": 60, "endColumn": 35}, {"ruleId": "207", "severity": 1, "message": "233", "line": 61, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 61, "endColumn": 14}, {"ruleId": "207", "severity": 1, "message": "234", "line": 61, "column": 16, "nodeType": "209", "messageId": "210", "endLine": 61, "endColumn": 23}, {"ruleId": "207", "severity": 1, "message": "235", "line": 1, "column": 17, "nodeType": "209", "messageId": "210", "endLine": 1, "endColumn": 22}, "import/first", "Import in body of module; reorder to top.", "ImportDeclaration", {"range": "236", "text": "237"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleOAuthCallback'. Either include it or remove the dependency array.", "ArrayExpression", ["238"], "@typescript-eslint/no-unused-vars", "'Post' is defined but never used.", "Identifier", "unusedVar", "'useMemo' is defined but never used.", "'Tooltip' is defined but never used.", "'FilterIcon' is defined but never used.", "'loading' is assigned a value but never used.", "'ScheduleOptions' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemAvatar' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'CropIcon' is defined but never used.", "'PaletteIcon' is defined but never used.", "'ZoomInIcon' is defined but never used.", "'ZoomOutIcon' is defined but never used.", "'selectedTool' is assigned a value but never used.", "'setSelectedTool' is assigned a value but never used.", "'saturation' is assigned a value but never used.", "'setSaturation' is assigned a value but never used.", "'zoom' is assigned a value but never used.", "'setZoom' is assigned a value but never used.", "'Dayjs' is defined but never used.", [0, 475], "import {\n  createUserWithEmailAndPassword,\n  signInWithEmailAndPassword,\n  signOut,\n  sendPasswordResetEmail,\n  sendEmailVerification,\n  updateProfile,\n  User as FirebaseUser,\n  GoogleAuthProvider,\n  signInWithPopup,\n  onAuthStateChanged,\n  NextOrObserver,\n} from 'firebase/auth';\nimport { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';\nimport { auth, db } from './config';\nimport { User } from '../../types';\n\n// Demo mode check\nconst isDemoMode = !auth || !db;", {"desc": "239", "fix": "240"}, "Update the dependencies array to be: [handleOAuthCallback, platform, searchParams]", {"range": "241", "text": "242"}, [979, 1003], "[handleOAuthCallback, platform, searchParams]"]