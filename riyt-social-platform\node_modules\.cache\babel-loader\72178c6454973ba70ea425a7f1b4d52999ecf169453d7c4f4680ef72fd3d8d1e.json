{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction toKey(value) {\n  if (typeof value === 'string' || typeof value === 'symbol') {\n    return value;\n  }\n  if (Object.is(value?.valueOf?.(), -0)) {\n    return '-0';\n  }\n  return String(value);\n}\nexports.toKey = toKey;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "to<PERSON><PERSON>", "is", "valueOf", "String"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/es-toolkit/dist/compat/_internal/toKey.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction toKey(value) {\n    if (typeof value === 'string' || typeof value === 'symbol') {\n        return value;\n    }\n    if (Object.is(value?.valueOf?.(), -0)) {\n        return '-0';\n    }\n    return String(value);\n}\n\nexports.toKey = toKey;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,KAAKA,CAACD,KAAK,EAAE;EAClB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACxD,OAAOA,KAAK;EAChB;EACA,IAAIL,MAAM,CAACO,EAAE,CAACF,KAAK,EAAEG,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IACnC,OAAO,IAAI;EACf;EACA,OAAOC,MAAM,CAACJ,KAAK,CAAC;AACxB;AAEAH,OAAO,CAACI,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}