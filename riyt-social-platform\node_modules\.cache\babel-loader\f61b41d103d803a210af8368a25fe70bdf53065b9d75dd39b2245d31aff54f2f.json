{"ast": null, "code": "module.exports = require('../dist/compat/array/uniqBy.js').uniqBy;", "map": {"version": 3, "names": ["module", "exports", "require", "uniqBy"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/es-toolkit/compat/uniqBy.js"], "sourcesContent": ["module.exports = require('../dist/compat/array/uniqBy.js').uniqBy;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,gCAAgC,CAAC,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}