{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarMonths } from \"./differenceInCalendarMonths.js\";\nimport { isLastDayOfMonth } from \"./isLastDayOfMonth.js\";\n\n/**\n * The {@link differenceInMonths} function options.\n */\n\n/**\n * @name differenceInMonths\n * @category Month Helpers\n * @summary Get the number of full months between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full months\n *\n * @example\n * // How many full months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInMonths(new Date(2014, 8, 1), new Date(2014, 0, 31))\n * //=> 7\n */\nexport function differenceInMonths(laterDate, earlierDate, options) {\n  const [laterDate_, workingLaterDate, earlierDate_] = normalizeDates(options?.in, laterDate, laterDate, earlierDate);\n  const sign = compareAsc(workingLaterDate, earlierDate_);\n  const difference = Math.abs(differenceInCalendarMonths(workingLaterDate, earlierDate_));\n  if (difference < 1) return 0;\n  if (workingLaterDate.getMonth() === 1 && workingLaterDate.getDate() > 27) workingLaterDate.setDate(30);\n  workingLaterDate.setMonth(workingLaterDate.getMonth() - sign * difference);\n  let isLastMonthNotFull = compareAsc(workingLaterDate, earlierDate_) === -sign;\n  if (isLastDayOfMonth(laterDate_) && difference === 1 && compareAsc(laterDate_, earlierDate_) === 1) {\n    isLastMonthNotFull = false;\n  }\n  const result = sign * (difference - +isLastMonthNotFull);\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInMonths;", "map": {"version": 3, "names": ["normalizeDates", "compareAsc", "differenceInCalendarMonths", "isLastDayOfMonth", "differenceInMonths", "laterDate", "earlierDate", "options", "laterDate_", "workingLaterDate", "earlierDate_", "in", "sign", "difference", "Math", "abs", "getMonth", "getDate", "setDate", "setMonth", "isLastMonthNotFull", "result"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/differenceInMonths.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarMonths } from \"./differenceInCalendarMonths.js\";\nimport { isLastDayOfMonth } from \"./isLastDayOfMonth.js\";\n\n/**\n * The {@link differenceInMonths} function options.\n */\n\n/**\n * @name differenceInMonths\n * @category Month Helpers\n * @summary Get the number of full months between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full months\n *\n * @example\n * // How many full months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInMonths(new Date(2014, 8, 1), new Date(2014, 0, 31))\n * //=> 7\n */\nexport function differenceInMonths(laterDate, earlierDate, options) {\n  const [laterDate_, workingLaterDate, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    laterDate,\n    earlierDate,\n  );\n\n  const sign = compareAsc(workingLaterDate, earlierDate_);\n  const difference = Math.abs(\n    differenceInCalendarMonths(workingLaterDate, earlierDate_),\n  );\n\n  if (difference < 1) return 0;\n\n  if (workingLaterDate.getMonth() === 1 && workingLaterDate.getDate() > 27)\n    workingLaterDate.setDate(30);\n\n  workingLaterDate.setMonth(workingLaterDate.getMonth() - sign * difference);\n\n  let isLastMonthNotFull = compareAsc(workingLaterDate, earlierDate_) === -sign;\n\n  if (\n    isLastDayOfMonth(laterDate_) &&\n    difference === 1 &&\n    compareAsc(laterDate_, earlierDate_) === 1\n  ) {\n    isLastMonthNotFull = false;\n  }\n\n  const result = sign * (difference - +isLastMonthNotFull);\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInMonths;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,gBAAgB,QAAQ,uBAAuB;;AAExD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAClE,MAAM,CAACC,UAAU,EAAEC,gBAAgB,EAAEC,YAAY,CAAC,GAAGV,cAAc,CACjEO,OAAO,EAAEI,EAAE,EACXN,SAAS,EACTA,SAAS,EACTC,WACF,CAAC;EAED,MAAMM,IAAI,GAAGX,UAAU,CAACQ,gBAAgB,EAAEC,YAAY,CAAC;EACvD,MAAMG,UAAU,GAAGC,IAAI,CAACC,GAAG,CACzBb,0BAA0B,CAACO,gBAAgB,EAAEC,YAAY,CAC3D,CAAC;EAED,IAAIG,UAAU,GAAG,CAAC,EAAE,OAAO,CAAC;EAE5B,IAAIJ,gBAAgB,CAACO,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAIP,gBAAgB,CAACQ,OAAO,CAAC,CAAC,GAAG,EAAE,EACtER,gBAAgB,CAACS,OAAO,CAAC,EAAE,CAAC;EAE9BT,gBAAgB,CAACU,QAAQ,CAACV,gBAAgB,CAACO,QAAQ,CAAC,CAAC,GAAGJ,IAAI,GAAGC,UAAU,CAAC;EAE1E,IAAIO,kBAAkB,GAAGnB,UAAU,CAACQ,gBAAgB,EAAEC,YAAY,CAAC,KAAK,CAACE,IAAI;EAE7E,IACET,gBAAgB,CAACK,UAAU,CAAC,IAC5BK,UAAU,KAAK,CAAC,IAChBZ,UAAU,CAACO,UAAU,EAAEE,YAAY,CAAC,KAAK,CAAC,EAC1C;IACAU,kBAAkB,GAAG,KAAK;EAC5B;EAEA,MAAMC,MAAM,GAAGT,IAAI,IAAIC,UAAU,GAAG,CAACO,kBAAkB,CAAC;EACxD,OAAOC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC;;AAEA;AACA,eAAejB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}