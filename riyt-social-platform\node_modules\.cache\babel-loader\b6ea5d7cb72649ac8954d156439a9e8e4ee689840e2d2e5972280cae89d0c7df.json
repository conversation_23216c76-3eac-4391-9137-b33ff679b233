{"ast": null, "code": "import * as React from 'react';\nimport { SetPolarGraphicalItem } from '../state/SetGraphicalItem';\nexport var PolarGraphicalItemContext = props => {\n  return /*#__PURE__*/React.createElement(SetPolarGraphicalItem, props);\n};", "map": {"version": 3, "names": ["React", "SetPolarGraphicalItem", "PolarGraphicalItemContext", "props", "createElement"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/context/PolarGraphicalItemContext.js"], "sourcesContent": ["import * as React from 'react';\nimport { SetPolarGraphicalItem } from '../state/SetGraphicalItem';\nexport var PolarGraphicalItemContext = props => {\n  return /*#__PURE__*/React.createElement(SetPolarGraphicalItem, props);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,OAAO,IAAIC,yBAAyB,GAAGC,KAAK,IAAI;EAC9C,OAAO,aAAaH,KAAK,CAACI,aAAa,CAACH,qBAAqB,EAAEE,KAAK,CAAC;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}