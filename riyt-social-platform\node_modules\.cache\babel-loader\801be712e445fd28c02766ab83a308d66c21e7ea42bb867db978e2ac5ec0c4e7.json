{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction toPath(deepKey) {\n  const result = [];\n  const length = deepKey.length;\n  if (length === 0) {\n    return result;\n  }\n  let index = 0;\n  let key = '';\n  let quoteChar = '';\n  let bracket = false;\n  if (deepKey.charCodeAt(0) === 46) {\n    result.push('');\n    index++;\n  }\n  while (index < length) {\n    const char = deepKey[index];\n    if (quoteChar) {\n      if (char === '\\\\' && index + 1 < length) {\n        index++;\n        key += deepKey[index];\n      } else if (char === quoteChar) {\n        quoteChar = '';\n      } else {\n        key += char;\n      }\n    } else if (bracket) {\n      if (char === '\"' || char === \"'\") {\n        quoteChar = char;\n      } else if (char === ']') {\n        bracket = false;\n        result.push(key);\n        key = '';\n      } else {\n        key += char;\n      }\n    } else {\n      if (char === '[') {\n        bracket = true;\n        if (key) {\n          result.push(key);\n          key = '';\n        }\n      } else if (char === '.') {\n        if (key) {\n          result.push(key);\n          key = '';\n        }\n      } else {\n        key += char;\n      }\n    }\n    index++;\n  }\n  if (key) {\n    result.push(key);\n  }\n  return result;\n}\nexports.toPath = toPath;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "to<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "result", "length", "index", "key", "quoteChar", "bracket", "charCodeAt", "push", "char"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/es-toolkit/dist/compat/util/toPath.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction toPath(deepKey) {\n    const result = [];\n    const length = deepKey.length;\n    if (length === 0) {\n        return result;\n    }\n    let index = 0;\n    let key = '';\n    let quoteChar = '';\n    let bracket = false;\n    if (deepKey.charCodeAt(0) === 46) {\n        result.push('');\n        index++;\n    }\n    while (index < length) {\n        const char = deepKey[index];\n        if (quoteChar) {\n            if (char === '\\\\' && index + 1 < length) {\n                index++;\n                key += deepKey[index];\n            }\n            else if (char === quoteChar) {\n                quoteChar = '';\n            }\n            else {\n                key += char;\n            }\n        }\n        else if (bracket) {\n            if (char === '\"' || char === \"'\") {\n                quoteChar = char;\n            }\n            else if (char === ']') {\n                bracket = false;\n                result.push(key);\n                key = '';\n            }\n            else {\n                key += char;\n            }\n        }\n        else {\n            if (char === '[') {\n                bracket = true;\n                if (key) {\n                    result.push(key);\n                    key = '';\n                }\n            }\n            else if (char === '.') {\n                if (key) {\n                    result.push(key);\n                    key = '';\n                }\n            }\n            else {\n                key += char;\n            }\n        }\n        index++;\n    }\n    if (key) {\n        result.push(key);\n    }\n    return result;\n}\n\nexports.toPath = toPath;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,MAAMA,CAACC,OAAO,EAAE;EACrB,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,MAAM,GAAGF,OAAO,CAACE,MAAM;EAC7B,IAAIA,MAAM,KAAK,CAAC,EAAE;IACd,OAAOD,MAAM;EACjB;EACA,IAAIE,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIN,OAAO,CAACO,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IAC9BN,MAAM,CAACO,IAAI,CAAC,EAAE,CAAC;IACfL,KAAK,EAAE;EACX;EACA,OAAOA,KAAK,GAAGD,MAAM,EAAE;IACnB,MAAMO,IAAI,GAAGT,OAAO,CAACG,KAAK,CAAC;IAC3B,IAAIE,SAAS,EAAE;MACX,IAAII,IAAI,KAAK,IAAI,IAAIN,KAAK,GAAG,CAAC,GAAGD,MAAM,EAAE;QACrCC,KAAK,EAAE;QACPC,GAAG,IAAIJ,OAAO,CAACG,KAAK,CAAC;MACzB,CAAC,MACI,IAAIM,IAAI,KAAKJ,SAAS,EAAE;QACzBA,SAAS,GAAG,EAAE;MAClB,CAAC,MACI;QACDD,GAAG,IAAIK,IAAI;MACf;IACJ,CAAC,MACI,IAAIH,OAAO,EAAE;MACd,IAAIG,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;QAC9BJ,SAAS,GAAGI,IAAI;MACpB,CAAC,MACI,IAAIA,IAAI,KAAK,GAAG,EAAE;QACnBH,OAAO,GAAG,KAAK;QACfL,MAAM,CAACO,IAAI,CAACJ,GAAG,CAAC;QAChBA,GAAG,GAAG,EAAE;MACZ,CAAC,MACI;QACDA,GAAG,IAAIK,IAAI;MACf;IACJ,CAAC,MACI;MACD,IAAIA,IAAI,KAAK,GAAG,EAAE;QACdH,OAAO,GAAG,IAAI;QACd,IAAIF,GAAG,EAAE;UACLH,MAAM,CAACO,IAAI,CAACJ,GAAG,CAAC;UAChBA,GAAG,GAAG,EAAE;QACZ;MACJ,CAAC,MACI,IAAIK,IAAI,KAAK,GAAG,EAAE;QACnB,IAAIL,GAAG,EAAE;UACLH,MAAM,CAACO,IAAI,CAACJ,GAAG,CAAC;UAChBA,GAAG,GAAG,EAAE;QACZ;MACJ,CAAC,MACI;QACDA,GAAG,IAAIK,IAAI;MACf;IACJ;IACAN,KAAK,EAAE;EACX;EACA,IAAIC,GAAG,EAAE;IACLH,MAAM,CAACO,IAAI,CAACJ,GAAG,CAAC;EACpB;EACA,OAAOH,MAAM;AACjB;AAEAN,OAAO,CAACI,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}