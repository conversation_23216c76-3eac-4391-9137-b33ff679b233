"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M14.04 7.17h-.08l-1.31 3.72h2.69z",
  opacity: ".3"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M8 16h12V4H8zm5.2-10.5h1.61l3.38 9h-1.56l-.8-2.3H12.2l-.82 2.3H9.81z",
  opacity: ".3"
}, "1"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M4 6H2v14c0 1.1.9 2 2 2h14v-2H4z"
}, "2"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 14H8V4h12z"
}, "3"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12.19 12.2h3.63l.8 2.3h1.56l-3.38-9h-1.6l-3.38 9h1.56zm1.77-5.03h.08l1.31 3.72h-2.69z"
}, "4")], 'TypeSpecimenTwoTone');