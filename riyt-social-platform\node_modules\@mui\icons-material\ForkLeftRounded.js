"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M15 20c0 .55-.45 1-1 1s-1-.45-1-1v-3c-.73-2.58-3.07-3.47-5.17-3l.88.88c.39.39.39 1.02 0 1.41s-1.02.39-1.41 0L4.71 13.7a.996.996 0 0 1 0-1.41L7.3 9.7c.39-.39 1.02-.39 1.41 0s.39 1.02 0 1.41l-.88.89c1.51-.33 3.73.08 5.17 1.36V6.83l-.88.88c-.39.39-1.02.39-1.41 0a.996.996 0 0 1 0-1.41l2.59-2.59c.39-.39 1.02-.39 1.41 0L17.3 6.3c.39.39.39 1.02 0 1.41s-1.02.39-1.41 0L15 6.83z"
}), 'ForkLeftRounded');