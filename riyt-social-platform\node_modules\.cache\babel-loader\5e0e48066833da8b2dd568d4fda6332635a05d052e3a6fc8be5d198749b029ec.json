{"ast": null, "code": "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link differenceInCalendarWeeks} function options.\n */\n\n/**\n * @name differenceInCalendarWeeks\n * @category Week Helpers\n * @summary Get the number of calendar weeks between the given dates.\n *\n * @description\n * Get the number of calendar weeks between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of calendar weeks\n *\n * @example\n * // How many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5)\n * )\n * //=> 3\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5),\n *   { weekStartsOn: 1 }\n * )\n * //=> 2\n */\nexport function differenceInCalendarWeeks(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  const laterStartOfWeek = startOfWeek(laterDate_, options);\n  const earlierStartOfWeek = startOfWeek(earlierDate_, options);\n  const laterTimestamp = +laterStartOfWeek - getTimezoneOffsetInMilliseconds(laterStartOfWeek);\n  const earlierTimestamp = +earlierStartOfWeek - getTimezoneOffsetInMilliseconds(earlierStartOfWeek);\n  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarWeeks;", "map": {"version": 3, "names": ["getTimezoneOffsetInMilliseconds", "normalizeDates", "millisecondsInWeek", "startOfWeek", "differenceInCalendarWeeks", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "laterStartOfWeek", "earlierStartOfWeek", "laterTimestamp", "earlierTimestamp", "Math", "round"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/differenceInCalendarWeeks.js"], "sourcesContent": ["import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link differenceInCalendarWeeks} function options.\n */\n\n/**\n * @name differenceInCalendarWeeks\n * @category Week Helpers\n * @summary Get the number of calendar weeks between the given dates.\n *\n * @description\n * Get the number of calendar weeks between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of calendar weeks\n *\n * @example\n * // How many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5)\n * )\n * //=> 3\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5),\n *   { weekStartsOn: 1 }\n * )\n * //=> 2\n */\nexport function differenceInCalendarWeeks(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const laterStartOfWeek = startOfWeek(laterDate_, options);\n  const earlierStartOfWeek = startOfWeek(earlierDate_, options);\n\n  const laterTimestamp =\n    +laterStartOfWeek - getTimezoneOffsetInMilliseconds(laterStartOfWeek);\n  const earlierTimestamp =\n    +earlierStartOfWeek - getTimezoneOffsetInMilliseconds(earlierStartOfWeek);\n\n  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarWeeks;\n"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,2CAA2C;AAC3F,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACzE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGR,cAAc,CAC/CM,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EAED,MAAMK,gBAAgB,GAAGR,WAAW,CAACK,UAAU,EAAED,OAAO,CAAC;EACzD,MAAMK,kBAAkB,GAAGT,WAAW,CAACM,YAAY,EAAEF,OAAO,CAAC;EAE7D,MAAMM,cAAc,GAClB,CAACF,gBAAgB,GAAGX,+BAA+B,CAACW,gBAAgB,CAAC;EACvE,MAAMG,gBAAgB,GACpB,CAACF,kBAAkB,GAAGZ,+BAA+B,CAACY,kBAAkB,CAAC;EAE3E,OAAOG,IAAI,CAACC,KAAK,CAAC,CAACH,cAAc,GAAGC,gBAAgB,IAAIZ,kBAAkB,CAAC;AAC7E;;AAEA;AACA,eAAeE,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}