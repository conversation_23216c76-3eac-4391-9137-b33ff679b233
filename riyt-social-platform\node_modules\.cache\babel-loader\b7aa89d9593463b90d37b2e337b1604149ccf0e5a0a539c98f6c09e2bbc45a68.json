{"ast": null, "code": "import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * ErrorBars have lot more settings but all the others are scoped to the component itself.\n * Only some of them required to be reported to the global store because <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> need to know\n * if the error bar is contributing to extending the axis domain.\n */\n\nvar initialState = {\n  countOfBars: 0,\n  cartesianItems: [],\n  polarItems: []\n};\nvar graphicalItemsSlice = createSlice({\n  name: 'graphicalItems',\n  initialState,\n  reducers: {\n    addBar(state) {\n      state.countOfBars += 1;\n    },\n    removeBar(state) {\n      state.countOfBars -= 1;\n    },\n    addCartesianGraphicalItem(state, action) {\n      state.cartesianItems.push(castDraft(action.payload));\n    },\n    replaceCartesianGraphicalItem(state, action) {\n      var {\n        prev,\n        next\n      } = action.payload;\n      var index = current(state).cartesianItems.indexOf(castDraft(prev));\n      if (index > -1) {\n        state.cartesianItems[index] = castDraft(next);\n      }\n    },\n    removeCartesianGraphicalItem(state, action) {\n      var index = current(state).cartesianItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.cartesianItems.splice(index, 1);\n      }\n    },\n    addPolarGraphicalItem(state, action) {\n      state.polarItems.push(castDraft(action.payload));\n    },\n    removePolarGraphicalItem(state, action) {\n      var index = current(state).polarItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.polarItems.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  addBar,\n  removeBar,\n  addCartesianGraphicalItem,\n  replaceCartesianGraphicalItem,\n  removeCartesianGraphicalItem,\n  addPolarGraphicalItem,\n  removePolarGraphicalItem\n} = graphicalItemsSlice.actions;\nexport var graphicalItemsReducer = graphicalItemsSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "current", "castDraft", "initialState", "countOfBars", "cartesianItems", "polarItems", "graphicalItemsSlice", "name", "reducers", "addBar", "state", "removeBar", "addCartesianGraphicalItem", "action", "push", "payload", "replaceCartesianGraphicalItem", "prev", "next", "index", "indexOf", "removeCartesianGraphicalItem", "splice", "addPolarGraphicalItem", "removePolarGraphicalItem", "actions", "graphicalItemsReducer", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/state/graphicalItemsSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * ErrorBars have lot more settings but all the others are scoped to the component itself.\n * Only some of them required to be reported to the global store because <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> need to know\n * if the error bar is contributing to extending the axis domain.\n */\n\nvar initialState = {\n  countOfBars: 0,\n  cartesianItems: [],\n  polarItems: []\n};\nvar graphicalItemsSlice = createSlice({\n  name: 'graphicalItems',\n  initialState,\n  reducers: {\n    addBar(state) {\n      state.countOfBars += 1;\n    },\n    removeBar(state) {\n      state.countOfBars -= 1;\n    },\n    addCartesianGraphicalItem(state, action) {\n      state.cartesianItems.push(castDraft(action.payload));\n    },\n    replaceCartesianGraphicalItem(state, action) {\n      var {\n        prev,\n        next\n      } = action.payload;\n      var index = current(state).cartesianItems.indexOf(castDraft(prev));\n      if (index > -1) {\n        state.cartesianItems[index] = castDraft(next);\n      }\n    },\n    removeCartesianGraphicalItem(state, action) {\n      var index = current(state).cartesianItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.cartesianItems.splice(index, 1);\n      }\n    },\n    addPolarGraphicalItem(state, action) {\n      state.polarItems.push(castDraft(action.payload));\n    },\n    removePolarGraphicalItem(state, action) {\n      var index = current(state).polarItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.polarItems.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  addBar,\n  removeBar,\n  addCartesianGraphicalItem,\n  replaceCartesianGraphicalItem,\n  removeCartesianGraphicalItem,\n  addPolarGraphicalItem,\n  removePolarGraphicalItem\n} = graphicalItemsSlice.actions;\nexport var graphicalItemsReducer = graphicalItemsSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,OAAO,QAAQ,kBAAkB;AACvD,SAASC,SAAS,QAAQ,OAAO;;AAEjC;AACA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAG;EACjBC,WAAW,EAAE,CAAC;EACdC,cAAc,EAAE,EAAE;EAClBC,UAAU,EAAE;AACd,CAAC;AACD,IAAIC,mBAAmB,GAAGP,WAAW,CAAC;EACpCQ,IAAI,EAAE,gBAAgB;EACtBL,YAAY;EACZM,QAAQ,EAAE;IACRC,MAAMA,CAACC,KAAK,EAAE;MACZA,KAAK,CAACP,WAAW,IAAI,CAAC;IACxB,CAAC;IACDQ,SAASA,CAACD,KAAK,EAAE;MACfA,KAAK,CAACP,WAAW,IAAI,CAAC;IACxB,CAAC;IACDS,yBAAyBA,CAACF,KAAK,EAAEG,MAAM,EAAE;MACvCH,KAAK,CAACN,cAAc,CAACU,IAAI,CAACb,SAAS,CAACY,MAAM,CAACE,OAAO,CAAC,CAAC;IACtD,CAAC;IACDC,6BAA6BA,CAACN,KAAK,EAAEG,MAAM,EAAE;MAC3C,IAAI;QACFI,IAAI;QACJC;MACF,CAAC,GAAGL,MAAM,CAACE,OAAO;MAClB,IAAII,KAAK,GAAGnB,OAAO,CAACU,KAAK,CAAC,CAACN,cAAc,CAACgB,OAAO,CAACnB,SAAS,CAACgB,IAAI,CAAC,CAAC;MAClE,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAE;QACdT,KAAK,CAACN,cAAc,CAACe,KAAK,CAAC,GAAGlB,SAAS,CAACiB,IAAI,CAAC;MAC/C;IACF,CAAC;IACDG,4BAA4BA,CAACX,KAAK,EAAEG,MAAM,EAAE;MAC1C,IAAIM,KAAK,GAAGnB,OAAO,CAACU,KAAK,CAAC,CAACN,cAAc,CAACgB,OAAO,CAACnB,SAAS,CAACY,MAAM,CAACE,OAAO,CAAC,CAAC;MAC5E,IAAII,KAAK,GAAG,CAAC,CAAC,EAAE;QACdT,KAAK,CAACN,cAAc,CAACkB,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACvC;IACF,CAAC;IACDI,qBAAqBA,CAACb,KAAK,EAAEG,MAAM,EAAE;MACnCH,KAAK,CAACL,UAAU,CAACS,IAAI,CAACb,SAAS,CAACY,MAAM,CAACE,OAAO,CAAC,CAAC;IAClD,CAAC;IACDS,wBAAwBA,CAACd,KAAK,EAAEG,MAAM,EAAE;MACtC,IAAIM,KAAK,GAAGnB,OAAO,CAACU,KAAK,CAAC,CAACL,UAAU,CAACe,OAAO,CAACnB,SAAS,CAACY,MAAM,CAACE,OAAO,CAAC,CAAC;MACxE,IAAII,KAAK,GAAG,CAAC,CAAC,EAAE;QACdT,KAAK,CAACL,UAAU,CAACiB,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACnC;IACF;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTV,MAAM;EACNE,SAAS;EACTC,yBAAyB;EACzBI,6BAA6B;EAC7BK,4BAA4B;EAC5BE,qBAAqB;EACrBC;AACF,CAAC,GAAGlB,mBAAmB,CAACmB,OAAO;AAC/B,OAAO,IAAIC,qBAAqB,GAAGpB,mBAAmB,CAACqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}