{"ast": null, "code": "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextTuesday} function options.\n */\n\n/**\n * @name nextTuesday\n * @category Weekday Helpers\n * @summary When is the next Tuesday?\n *\n * @description\n * When is the next Tuesday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Tuesday\n *\n * @example\n * // When is the next Tuesday after Mar, 22, 2020?\n * const result = nextTuesday(new Date(2020, 2, 22))\n * //=> Tue Mar 24 2020 00:00:00\n */\nexport function nextTuesday(date, options) {\n  return nextDay(date, 2, options);\n}\n\n// Fallback for modularized imports:\nexport default nextTuesday;", "map": {"version": 3, "names": ["nextDay", "nextTuesday", "date", "options"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/nextTuesday.js"], "sourcesContent": ["import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextTuesday} function options.\n */\n\n/**\n * @name nextTuesday\n * @category Weekday Helpers\n * @summary When is the next Tuesday?\n *\n * @description\n * When is the next Tuesday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Tuesday\n *\n * @example\n * // When is the next Tuesday after Mar, 22, 2020?\n * const result = nextTuesday(new Date(2020, 2, 22))\n * //=> Tue Mar 24 2020 00:00:00\n */\nexport function nextTuesday(date, options) {\n  return nextDay(date, 2, options);\n}\n\n// Fallback for modularized imports:\nexport default nextTuesday;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,OAAOH,OAAO,CAACE,IAAI,EAAE,CAAC,EAAEC,OAAO,CAAC;AAClC;;AAEA;AACA,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}