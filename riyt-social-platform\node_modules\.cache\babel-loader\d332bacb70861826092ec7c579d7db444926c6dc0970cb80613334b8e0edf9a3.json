{"ast": null, "code": "import { linearish } from \"./linear.js\";\nimport number from \"./number.js\";\nexport default function identity(domain) {\n  var unknown;\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n  scale.invert = scale;\n  scale.domain = scale.range = function (_) {\n    return arguments.length ? (domain = Array.from(_, number), scale) : domain.slice();\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.copy = function () {\n    return identity(domain).unknown(unknown);\n  };\n  domain = arguments.length ? Array.from(domain, number) : [0, 1];\n  return linearish(scale);\n}", "map": {"version": 3, "names": ["linearish", "number", "identity", "domain", "unknown", "scale", "x", "isNaN", "invert", "range", "_", "arguments", "length", "Array", "from", "slice", "copy"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-scale/src/identity.js"], "sourcesContent": ["import {linearish} from \"./linear.js\";\nimport number from \"./number.js\";\n\nexport default function identity(domain) {\n  var unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n\n  scale.invert = scale;\n\n  scale.domain = scale.range = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), scale) : domain.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return identity(domain).unknown(unknown);\n  };\n\n  domain = arguments.length ? Array.from(domain, number) : [0, 1];\n\n  return linearish(scale);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,aAAa;AACrC,OAAOC,MAAM,MAAM,aAAa;AAEhC,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAE;EACvC,IAAIC,OAAO;EAEX,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGF,OAAO,GAAGE,CAAC;EACjD;EAEAD,KAAK,CAACG,MAAM,GAAGH,KAAK;EAEpBA,KAAK,CAACF,MAAM,GAAGE,KAAK,CAACI,KAAK,GAAG,UAASC,CAAC,EAAE;IACvC,OAAOC,SAAS,CAACC,MAAM,IAAIT,MAAM,GAAGU,KAAK,CAACC,IAAI,CAACJ,CAAC,EAAET,MAAM,CAAC,EAAEI,KAAK,IAAIF,MAAM,CAACY,KAAK,CAAC,CAAC;EACpF,CAAC;EAEDV,KAAK,CAACD,OAAO,GAAG,UAASM,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAIR,OAAO,GAAGM,CAAC,EAAEL,KAAK,IAAID,OAAO;EAC1D,CAAC;EAEDC,KAAK,CAACW,IAAI,GAAG,YAAW;IACtB,OAAOd,QAAQ,CAACC,MAAM,CAAC,CAACC,OAAO,CAACA,OAAO,CAAC;EAC1C,CAAC;EAEDD,MAAM,GAAGQ,SAAS,CAACC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACX,MAAM,EAAEF,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAE/D,OAAOD,SAAS,CAACK,KAAK,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}