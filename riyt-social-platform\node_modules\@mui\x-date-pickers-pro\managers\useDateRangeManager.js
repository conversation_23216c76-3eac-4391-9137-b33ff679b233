"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useDateRangeManager = useDateRangeManager;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _hooks = require("@mui/x-date-pickers/hooks");
var _internals = require("@mui/x-date-pickers/internals");
var _valueManagers = require("../internals/utils/valueManagers");
var _validation = require("../validation");
var _dateUtils = require("../internals/utils/date-utils");
function useDateRangeManager(parameters = {}) {
  const {
    enableAccessibleFieldDOMStructure = true,
    dateSeparator
  } = parameters;
  return React.useMemo(() => ({
    valueType: 'date',
    validator: _validation.validateDateRange,
    internal_valueManager: _valueManagers.rangeValueManager,
    internal_fieldValueManager: (0, _valueManagers.getRangeFieldValueManager)({
      dateSeparator
    }),
    internal_enableAccessibleFieldDOMStructure: enableAccessibleFieldDOMStructure,
    internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToDateRangeFieldInternalProps,
    internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel
  }), [enableAccessibleFieldDOMStructure, dateSeparator]);
}
function useOpenPickerButtonAriaLabel(value) {
  const adapter = (0, _hooks.usePickerAdapter)();
  const translations = (0, _hooks.usePickerTranslations)();
  return React.useMemo(() => {
    return translations.openRangePickerDialogue((0, _dateUtils.formatRange)(adapter, value, 'fullDate'));
  }, [value, translations, adapter]);
}
function useApplyDefaultValuesToDateRangeFieldInternalProps(internalProps) {
  const adapter = (0, _hooks.usePickerAdapter)();
  const validationProps = (0, _internals.useApplyDefaultValuesToDateValidationProps)(internalProps);
  return React.useMemo(() => (0, _extends2.default)({}, internalProps, validationProps, {
    format: internalProps.format ?? adapter.formats.keyboardDate
  }), [internalProps, validationProps, adapter]);
}