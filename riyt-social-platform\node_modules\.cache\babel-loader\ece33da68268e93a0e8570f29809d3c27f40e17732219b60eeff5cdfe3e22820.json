{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfMonth} function options.\n */\n\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of a month\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(0, 0, 0, 0);\n  return toDate(_date, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfMonth;", "map": {"version": 3, "names": ["toDate", "lastDayOfMonth", "date", "options", "_date", "in", "month", "getMonth", "setFullYear", "getFullYear", "setHours"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/lastDayOfMonth.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfMonth} function options.\n */\n\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of a month\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(0, 0, 0, 0);\n  return toDate(_date, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfMonth;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC5C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,KAAK,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC;EAC9BH,KAAK,CAACI,WAAW,CAACJ,KAAK,CAACK,WAAW,CAAC,CAAC,EAAEH,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EACpDF,KAAK,CAACM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,OAAOV,MAAM,CAACI,KAAK,EAAED,OAAO,EAAEE,EAAE,CAAC;AACnC;;AAEA;AACA,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}