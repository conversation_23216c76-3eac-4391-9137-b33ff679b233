{"ast": null, "code": "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInDay } from \"./constants.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link differenceInCalendarDays} function options.\n */\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options object\n *\n * @returns The number of calendar days\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport function differenceInCalendarDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  const laterStartOfDay = startOfDay(laterDate_);\n  const earlierStartOfDay = startOfDay(earlierDate_);\n  const laterTimestamp = +laterStartOfDay - getTimezoneOffsetInMilliseconds(laterStartOfDay);\n  const earlierTimestamp = +earlierStartOfDay - getTimezoneOffsetInMilliseconds(earlierStartOfDay);\n\n  // Round the number of days to the nearest integer because the number of\n  // milliseconds in a day is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarDays;", "map": {"version": 3, "names": ["getTimezoneOffsetInMilliseconds", "normalizeDates", "millisecondsInDay", "startOfDay", "differenceInCalendarDays", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "laterStartOfDay", "earlierStartOfDay", "laterTimestamp", "earlierTimestamp", "Math", "round"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/differenceInCalendarDays.js"], "sourcesContent": ["import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInDay } from \"./constants.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link differenceInCalendarDays} function options.\n */\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options object\n *\n * @returns The number of calendar days\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport function differenceInCalendarDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const laterStartOfDay = startOfDay(laterDate_);\n  const earlierStartOfDay = startOfDay(earlierDate_);\n\n  const laterTimestamp =\n    +laterStartOfDay - getTimezoneOffsetInMilliseconds(laterStartOfDay);\n  const earlierTimestamp =\n    +earlierStartOfDay - getTimezoneOffsetInMilliseconds(earlierStartOfDay);\n\n  // Round the number of days to the nearest integer because the number of\n  // milliseconds in a day is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarDays;\n"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,2CAA2C;AAC3F,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACxE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGR,cAAc,CAC/CM,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EAED,MAAMK,eAAe,GAAGR,UAAU,CAACK,UAAU,CAAC;EAC9C,MAAMI,iBAAiB,GAAGT,UAAU,CAACM,YAAY,CAAC;EAElD,MAAMI,cAAc,GAClB,CAACF,eAAe,GAAGX,+BAA+B,CAACW,eAAe,CAAC;EACrE,MAAMG,gBAAgB,GACpB,CAACF,iBAAiB,GAAGZ,+BAA+B,CAACY,iBAAiB,CAAC;;EAEzE;EACA;EACA;EACA,OAAOG,IAAI,CAACC,KAAK,CAAC,CAACH,cAAc,GAAGC,gBAAgB,IAAIZ,iBAAiB,CAAC;AAC5E;;AAEA;AACA,eAAeE,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}