{"ast": null, "code": "import { millisecondsInHour } from \"./constants.js\";\n\n/**\n * @name millisecondsToHours\n * @category Conversion Helpers\n * @summary Convert milliseconds to hours.\n *\n * @description\n * Convert a number of milliseconds to a full number of hours.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in hours\n *\n * @example\n * // Convert 7200000 milliseconds to hours:\n * const result = millisecondsToHours(7200000)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToHours(7199999)\n * //=> 1\n */\nexport function millisecondsToHours(milliseconds) {\n  const hours = milliseconds / millisecondsInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToHours;", "map": {"version": 3, "names": ["millisecondsInHour", "millisecondsToHours", "milliseconds", "hours", "Math", "trunc"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/millisecondsToHours.js"], "sourcesContent": ["import { millisecondsInHour } from \"./constants.js\";\n\n/**\n * @name millisecondsToHours\n * @category Conversion Helpers\n * @summary Convert milliseconds to hours.\n *\n * @description\n * Convert a number of milliseconds to a full number of hours.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in hours\n *\n * @example\n * // Convert 7200000 milliseconds to hours:\n * const result = millisecondsToHours(7200000)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToHours(7199999)\n * //=> 1\n */\nexport function millisecondsToHours(milliseconds) {\n  const hours = milliseconds / millisecondsInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToHours;\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,gBAAgB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,YAAY,EAAE;EAChD,MAAMC,KAAK,GAAGD,YAAY,GAAGF,kBAAkB;EAC/C,OAAOI,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1B;;AAEA;AACA,eAAeF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}