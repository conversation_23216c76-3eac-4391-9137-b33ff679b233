{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useControlled from '@mui/utils/useControlled';\nimport useTimeout from '@mui/utils/useTimeout';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { usePickerAdapter, usePickerTranslations } from \"../../../hooks/index.js\";\nimport { mergeDateIntoReferenceDate, getSectionsBoundaries, validateSections, getDateFromDateSections, parseSelectedSections, getLocalizedDigits, getSectionOrder } from \"./useField.utils.js\";\nimport { buildSectionsFromFormat } from \"./buildSectionsFromFormat.js\";\nimport { useValidation } from \"../../../validation/index.js\";\nimport { useControlledValue } from \"../useControlledValue.js\";\nimport { getSectionTypeGranularity } from \"../../utils/getDefaultReferenceDate.js\";\nconst QUERY_LIFE_DURATION_MS = 5000;\nexport const useFieldState = parameters => {\n  const adapter = usePickerAdapter();\n  const translations = usePickerTranslations();\n  const isRtl = useRtl();\n  const {\n    manager: {\n      validator,\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager\n    },\n    internalPropsWithDefaults,\n    internalPropsWithDefaults: {\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      onChange,\n      format,\n      formatDensity = 'dense',\n      selectedSections: selectedSectionsProp,\n      onSelectedSectionsChange,\n      shouldRespectLeadingZeros = false,\n      timezone: timezoneProp,\n      enableAccessibleFieldDOMStructure = true\n    },\n    forwardedProps: {\n      error: errorProp\n    }\n  } = parameters;\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'a field component',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager\n  });\n  const valueRef = React.useRef(value);\n  React.useEffect(() => {\n    valueRef.current = value;\n  }, [value]);\n  const {\n    hasValidationError\n  } = useValidation({\n    props: internalPropsWithDefaults,\n    validator,\n    timezone,\n    value,\n    onError: internalPropsWithDefaults.onError\n  });\n  const error = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (errorProp !== undefined) {\n      return errorProp;\n    }\n    return hasValidationError;\n  }, [hasValidationError, errorProp]);\n  const localizedDigits = React.useMemo(() => getLocalizedDigits(adapter), [adapter]);\n  const sectionsValueBoundaries = React.useMemo(() => getSectionsBoundaries(adapter, localizedDigits, timezone), [adapter, localizedDigits, timezone]);\n  const getSectionsFromValue = React.useCallback(valueToAnalyze => fieldValueManager.getSectionsFromValue(valueToAnalyze, date => buildSectionsFromFormat({\n    adapter,\n    localeText: translations,\n    localizedDigits,\n    format,\n    date,\n    formatDensity,\n    shouldRespectLeadingZeros,\n    enableAccessibleFieldDOMStructure,\n    isRtl\n  })), [fieldValueManager, format, translations, localizedDigits, isRtl, shouldRespectLeadingZeros, adapter, formatDensity, enableAccessibleFieldDOMStructure]);\n  const [state, setState] = React.useState(() => {\n    const sections = getSectionsFromValue(value);\n    validateSections(sections, valueType);\n    const stateWithoutReferenceDate = {\n      sections,\n      lastExternalValue: value,\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      tempValueStrAndroid: null,\n      characterQuery: null\n    };\n    const granularity = getSectionTypeGranularity(sections);\n    const referenceValue = valueManager.getInitialReferenceValue({\n      referenceDate: referenceDateProp,\n      value,\n      adapter,\n      props: internalPropsWithDefaults,\n      granularity,\n      timezone\n    });\n    return _extends({}, stateWithoutReferenceDate, {\n      referenceValue\n    });\n  });\n  const [selectedSections, innerSetSelectedSections] = useControlled({\n    controlled: selectedSectionsProp,\n    default: null,\n    name: 'useField',\n    state: 'selectedSections'\n  });\n  const setSelectedSections = newSelectedSections => {\n    innerSetSelectedSections(newSelectedSections);\n    onSelectedSectionsChange?.(newSelectedSections);\n  };\n  const parsedSelectedSections = React.useMemo(() => parseSelectedSections(selectedSections, state.sections), [selectedSections, state.sections]);\n  const activeSectionIndex = parsedSelectedSections === 'all' ? 0 : parsedSelectedSections;\n  const sectionOrder = React.useMemo(() => getSectionOrder(state.sections, isRtl && !enableAccessibleFieldDOMStructure), [state.sections, isRtl, enableAccessibleFieldDOMStructure]);\n  const areAllSectionsEmpty = React.useMemo(() => state.sections.every(section => section.value === ''), [state.sections]);\n  const publishValue = newValue => {\n    const context = {\n      validationError: validator({\n        adapter,\n        value: newValue,\n        timezone,\n        props: internalPropsWithDefaults\n      })\n    };\n    handleValueChange(newValue, context);\n  };\n  const setSectionValue = (sectionIndex, newSectionValue) => {\n    const newSections = [...state.sections];\n    newSections[sectionIndex] = _extends({}, newSections[sectionIndex], {\n      value: newSectionValue,\n      modified: true\n    });\n    return newSections;\n  };\n  const sectionToUpdateOnNextInvalidDateRef = React.useRef(null);\n  const updateSectionValueOnNextInvalidDateTimeout = useTimeout();\n  const setSectionUpdateToApplyOnNextInvalidDate = newSectionValue => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    sectionToUpdateOnNextInvalidDateRef.current = {\n      sectionIndex: activeSectionIndex,\n      value: newSectionValue\n    };\n    updateSectionValueOnNextInvalidDateTimeout.start(0, () => {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    });\n  };\n  const clearValue = () => {\n    if (valueManager.areValuesEqual(adapter, value, valueManager.emptyValue)) {\n      setState(prevState => _extends({}, prevState, {\n        sections: prevState.sections.map(section => _extends({}, section, {\n          value: ''\n        })),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => _extends({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(valueManager.emptyValue);\n    }\n  };\n  const clearActiveSection = () => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    const activeSection = state.sections[activeSectionIndex];\n    if (activeSection.value === '') {\n      return;\n    }\n    setSectionUpdateToApplyOnNextInvalidDate('');\n    if (fieldValueManager.getDateFromSection(value, activeSection) === null) {\n      setState(prevState => _extends({}, prevState, {\n        sections: setSectionValue(activeSectionIndex, ''),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => _extends({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(fieldValueManager.updateDateInValue(value, activeSection, null));\n    }\n  };\n  const updateValueFromValueStr = valueStr => {\n    const parseDateStr = (dateStr, referenceDate) => {\n      const date = adapter.parse(dateStr, format);\n      if (!adapter.isValid(date)) {\n        return null;\n      }\n      const sections = buildSectionsFromFormat({\n        adapter,\n        localeText: translations,\n        localizedDigits,\n        format,\n        date,\n        formatDensity,\n        shouldRespectLeadingZeros,\n        enableAccessibleFieldDOMStructure,\n        isRtl\n      });\n      return mergeDateIntoReferenceDate(adapter, date, sections, referenceDate, false);\n    };\n    const newValue = fieldValueManager.parseValueStr(valueStr, state.referenceValue, parseDateStr);\n    publishValue(newValue);\n  };\n  const cleanActiveDateSectionsIfValueNullTimeout = useTimeout();\n  const updateSectionValue = ({\n    section,\n    newSectionValue,\n    shouldGoToNextSection\n  }) => {\n    updateSectionValueOnNextInvalidDateTimeout.clear();\n    cleanActiveDateSectionsIfValueNullTimeout.clear();\n    const activeDate = fieldValueManager.getDateFromSection(value, section);\n\n    /**\n     * Decide which section should be focused\n     */\n    if (shouldGoToNextSection && activeSectionIndex < state.sections.length - 1) {\n      setSelectedSections(activeSectionIndex + 1);\n    }\n\n    /**\n     * Try to build a valid date from the new section value\n     */\n    const newSections = setSectionValue(activeSectionIndex, newSectionValue);\n    const newActiveDateSections = fieldValueManager.getDateSectionsFromValue(newSections, section);\n    const newActiveDate = getDateFromDateSections(adapter, newActiveDateSections, localizedDigits);\n\n    /**\n     * If the new date is valid,\n     * Then we merge the value of the modified sections into the reference date.\n     * This makes sure that we don't lose some information of the initial date (like the time on a date field).\n     */\n    if (adapter.isValid(newActiveDate)) {\n      const mergedDate = mergeDateIntoReferenceDate(adapter, newActiveDate, newActiveDateSections, fieldValueManager.getDateFromSection(state.referenceValue, section), true);\n      if (activeDate == null) {\n        cleanActiveDateSectionsIfValueNullTimeout.start(0, () => {\n          if (valueRef.current === value) {\n            setState(prevState => _extends({}, prevState, {\n              sections: fieldValueManager.clearDateSections(state.sections, section),\n              tempValueStrAndroid: null\n            }));\n          }\n        });\n      }\n      return publishValue(fieldValueManager.updateDateInValue(value, section, mergedDate));\n    }\n\n    /**\n     * If all the sections are filled but the date is invalid and the previous date is valid or null,\n     * Then we publish an invalid date.\n     */\n    if (newActiveDateSections.every(sectionBis => sectionBis.value !== '') && (activeDate == null || adapter.isValid(activeDate))) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, newActiveDate));\n    }\n\n    /**\n     * If the previous date is not null,\n     * Then we publish the date as `null`.\n     */\n    if (activeDate != null) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, null));\n    }\n\n    /**\n     * If the previous date is already null,\n     * Then we don't publish the date and we update the sections.\n     */\n    return setState(prevState => _extends({}, prevState, {\n      sections: newSections,\n      tempValueStrAndroid: null\n    }));\n  };\n  const setTempAndroidValueStr = tempValueStrAndroid => setState(prevState => _extends({}, prevState, {\n    tempValueStrAndroid\n  }));\n  const setCharacterQuery = useEventCallback(newCharacterQuery => {\n    setState(prevState => _extends({}, prevState, {\n      characterQuery: newCharacterQuery\n    }));\n  });\n\n  // If `prop.value` changes, we update the state to reflect the new value\n  if (value !== state.lastExternalValue) {\n    let sections;\n    if (sectionToUpdateOnNextInvalidDateRef.current != null && !adapter.isValid(fieldValueManager.getDateFromSection(value, state.sections[sectionToUpdateOnNextInvalidDateRef.current.sectionIndex]))) {\n      sections = setSectionValue(sectionToUpdateOnNextInvalidDateRef.current.sectionIndex, sectionToUpdateOnNextInvalidDateRef.current.value);\n    } else {\n      sections = getSectionsFromValue(value);\n    }\n    setState(prevState => _extends({}, prevState, {\n      lastExternalValue: value,\n      sections,\n      sectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      referenceValue: fieldValueManager.updateReferenceValue(adapter, value, prevState.referenceValue),\n      tempValueStrAndroid: null\n    }));\n  }\n  if (isRtl !== state.lastSectionsDependencies.isRtl || format !== state.lastSectionsDependencies.format || adapter.locale !== state.lastSectionsDependencies.locale) {\n    const sections = getSectionsFromValue(value);\n    validateSections(sections, valueType);\n    setState(prevState => _extends({}, prevState, {\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      sections,\n      tempValueStrAndroid: null,\n      characterQuery: null\n    }));\n  }\n  if (state.characterQuery != null && !error && activeSectionIndex == null) {\n    setCharacterQuery(null);\n  }\n  if (state.characterQuery != null && state.sections[state.characterQuery.sectionIndex]?.type !== state.characterQuery.sectionType) {\n    setCharacterQuery(null);\n  }\n  React.useEffect(() => {\n    if (sectionToUpdateOnNextInvalidDateRef.current != null) {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    }\n  });\n  const cleanCharacterQueryTimeout = useTimeout();\n  React.useEffect(() => {\n    if (state.characterQuery != null) {\n      cleanCharacterQueryTimeout.start(QUERY_LIFE_DURATION_MS, () => setCharacterQuery(null));\n    }\n    return () => {};\n  }, [state.characterQuery, setCharacterQuery, cleanCharacterQueryTimeout]);\n\n  // If `tempValueStrAndroid` is still defined for some section when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && activeSectionIndex != null) {\n      clearActiveSection();\n    }\n  }, [state.sections]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  return {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    sectionsValueBoundaries,\n    state,\n    timezone,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useControlled", "useTimeout", "useEventCallback", "useRtl", "usePickerAdapter", "usePickerTranslations", "mergeDateIntoReferenceDate", "getSectionsBoundaries", "validateSections", "getDateFromDateSections", "parseSelectedSections", "getLocalizedDigits", "getSectionOrder", "buildSectionsFromFormat", "useValidation", "useControlledValue", "getSectionTypeGranularity", "QUERY_LIFE_DURATION_MS", "useFieldState", "parameters", "adapter", "translations", "isRtl", "manager", "validator", "valueType", "internal_valueManager", "valueManager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "internalPropsWithDefaults", "value", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "onChange", "format", "formatDensity", "selectedSections", "selectedSectionsProp", "onSelectedSectionsChange", "shouldRespectLeadingZeros", "timezone", "timezoneProp", "enableAccessibleFieldDOMStructure", "forwardedProps", "error", "errorProp", "handleValueChange", "name", "valueRef", "useRef", "useEffect", "current", "hasValidationError", "props", "onError", "useMemo", "undefined", "localizedDigits", "sectionsValueBoundaries", "getSectionsFromValue", "useCallback", "valueToAnalyze", "date", "localeText", "state", "setState", "useState", "sections", "stateWithoutReferenceDate", "lastExternalValue", "lastSectionsDependencies", "locale", "tempValueStrAndroid", "<PERSON><PERSON><PERSON><PERSON>", "granularity", "referenceValue", "getInitialReferenceValue", "innerSetSelectedSections", "controlled", "default", "setSelectedSections", "newSelectedSections", "parsedSelectedSections", "activeSectionIndex", "sectionOrder", "areAllSectionsEmpty", "every", "section", "publishValue", "newValue", "context", "validationError", "setSectionValue", "sectionIndex", "newSectionValue", "newSections", "modified", "sectionToUpdateOnNextInvalidDateRef", "updateSectionValueOnNextInvalidDateTimeout", "setSectionUpdateToApplyOnNextInvalidDate", "start", "clearValue", "areValuesEqual", "emptyValue", "prevState", "map", "clearActiveSection", "activeSection", "getDateFromSection", "updateDateInValue", "updateValueFromValueStr", "valueStr", "parseDateStr", "dateStr", "parse", "<PERSON><PERSON><PERSON><PERSON>", "parseValueStr", "cleanActiveDateSectionsIfValueNullTimeout", "updateSectionValue", "shouldGoToNextSection", "clear", "activeDate", "length", "newActiveDateSections", "getDateSectionsFromValue", "newActiveDate", "mergedDate", "clearDateSections", "sectionBis", "setTempAndroidValueStr", "setCharacterQuery", "newCharacterQuery", "sectionsDependencies", "updateReferenceValue", "type", "sectionType", "cleanCharacterQueryTimeout"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldState.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useControlled from '@mui/utils/useControlled';\nimport useTimeout from '@mui/utils/useTimeout';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { usePickerAdapter, usePickerTranslations } from \"../../../hooks/index.js\";\nimport { mergeDateIntoReferenceDate, getSectionsBoundaries, validateSections, getDateFromDateSections, parseSelectedSections, getLocalizedDigits, getSectionOrder } from \"./useField.utils.js\";\nimport { buildSectionsFromFormat } from \"./buildSectionsFromFormat.js\";\nimport { useValidation } from \"../../../validation/index.js\";\nimport { useControlledValue } from \"../useControlledValue.js\";\nimport { getSectionTypeGranularity } from \"../../utils/getDefaultReferenceDate.js\";\nconst QUERY_LIFE_DURATION_MS = 5000;\nexport const useFieldState = parameters => {\n  const adapter = usePickerAdapter();\n  const translations = usePickerTranslations();\n  const isRtl = useRtl();\n  const {\n    manager: {\n      validator,\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager\n    },\n    internalPropsWithDefaults,\n    internalPropsWithDefaults: {\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      onChange,\n      format,\n      formatDensity = 'dense',\n      selectedSections: selectedSectionsProp,\n      onSelectedSectionsChange,\n      shouldRespectLeadingZeros = false,\n      timezone: timezoneProp,\n      enableAccessibleFieldDOMStructure = true\n    },\n    forwardedProps: {\n      error: errorProp\n    }\n  } = parameters;\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'a field component',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager\n  });\n  const valueRef = React.useRef(value);\n  React.useEffect(() => {\n    valueRef.current = value;\n  }, [value]);\n  const {\n    hasValidationError\n  } = useValidation({\n    props: internalPropsWithDefaults,\n    validator,\n    timezone,\n    value,\n    onError: internalPropsWithDefaults.onError\n  });\n  const error = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (errorProp !== undefined) {\n      return errorProp;\n    }\n    return hasValidationError;\n  }, [hasValidationError, errorProp]);\n  const localizedDigits = React.useMemo(() => getLocalizedDigits(adapter), [adapter]);\n  const sectionsValueBoundaries = React.useMemo(() => getSectionsBoundaries(adapter, localizedDigits, timezone), [adapter, localizedDigits, timezone]);\n  const getSectionsFromValue = React.useCallback(valueToAnalyze => fieldValueManager.getSectionsFromValue(valueToAnalyze, date => buildSectionsFromFormat({\n    adapter,\n    localeText: translations,\n    localizedDigits,\n    format,\n    date,\n    formatDensity,\n    shouldRespectLeadingZeros,\n    enableAccessibleFieldDOMStructure,\n    isRtl\n  })), [fieldValueManager, format, translations, localizedDigits, isRtl, shouldRespectLeadingZeros, adapter, formatDensity, enableAccessibleFieldDOMStructure]);\n  const [state, setState] = React.useState(() => {\n    const sections = getSectionsFromValue(value);\n    validateSections(sections, valueType);\n    const stateWithoutReferenceDate = {\n      sections,\n      lastExternalValue: value,\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      tempValueStrAndroid: null,\n      characterQuery: null\n    };\n    const granularity = getSectionTypeGranularity(sections);\n    const referenceValue = valueManager.getInitialReferenceValue({\n      referenceDate: referenceDateProp,\n      value,\n      adapter,\n      props: internalPropsWithDefaults,\n      granularity,\n      timezone\n    });\n    return _extends({}, stateWithoutReferenceDate, {\n      referenceValue\n    });\n  });\n  const [selectedSections, innerSetSelectedSections] = useControlled({\n    controlled: selectedSectionsProp,\n    default: null,\n    name: 'useField',\n    state: 'selectedSections'\n  });\n  const setSelectedSections = newSelectedSections => {\n    innerSetSelectedSections(newSelectedSections);\n    onSelectedSectionsChange?.(newSelectedSections);\n  };\n  const parsedSelectedSections = React.useMemo(() => parseSelectedSections(selectedSections, state.sections), [selectedSections, state.sections]);\n  const activeSectionIndex = parsedSelectedSections === 'all' ? 0 : parsedSelectedSections;\n  const sectionOrder = React.useMemo(() => getSectionOrder(state.sections, isRtl && !enableAccessibleFieldDOMStructure), [state.sections, isRtl, enableAccessibleFieldDOMStructure]);\n  const areAllSectionsEmpty = React.useMemo(() => state.sections.every(section => section.value === ''), [state.sections]);\n  const publishValue = newValue => {\n    const context = {\n      validationError: validator({\n        adapter,\n        value: newValue,\n        timezone,\n        props: internalPropsWithDefaults\n      })\n    };\n    handleValueChange(newValue, context);\n  };\n  const setSectionValue = (sectionIndex, newSectionValue) => {\n    const newSections = [...state.sections];\n    newSections[sectionIndex] = _extends({}, newSections[sectionIndex], {\n      value: newSectionValue,\n      modified: true\n    });\n    return newSections;\n  };\n  const sectionToUpdateOnNextInvalidDateRef = React.useRef(null);\n  const updateSectionValueOnNextInvalidDateTimeout = useTimeout();\n  const setSectionUpdateToApplyOnNextInvalidDate = newSectionValue => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    sectionToUpdateOnNextInvalidDateRef.current = {\n      sectionIndex: activeSectionIndex,\n      value: newSectionValue\n    };\n    updateSectionValueOnNextInvalidDateTimeout.start(0, () => {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    });\n  };\n  const clearValue = () => {\n    if (valueManager.areValuesEqual(adapter, value, valueManager.emptyValue)) {\n      setState(prevState => _extends({}, prevState, {\n        sections: prevState.sections.map(section => _extends({}, section, {\n          value: ''\n        })),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => _extends({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(valueManager.emptyValue);\n    }\n  };\n  const clearActiveSection = () => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    const activeSection = state.sections[activeSectionIndex];\n    if (activeSection.value === '') {\n      return;\n    }\n    setSectionUpdateToApplyOnNextInvalidDate('');\n    if (fieldValueManager.getDateFromSection(value, activeSection) === null) {\n      setState(prevState => _extends({}, prevState, {\n        sections: setSectionValue(activeSectionIndex, ''),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => _extends({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(fieldValueManager.updateDateInValue(value, activeSection, null));\n    }\n  };\n  const updateValueFromValueStr = valueStr => {\n    const parseDateStr = (dateStr, referenceDate) => {\n      const date = adapter.parse(dateStr, format);\n      if (!adapter.isValid(date)) {\n        return null;\n      }\n      const sections = buildSectionsFromFormat({\n        adapter,\n        localeText: translations,\n        localizedDigits,\n        format,\n        date,\n        formatDensity,\n        shouldRespectLeadingZeros,\n        enableAccessibleFieldDOMStructure,\n        isRtl\n      });\n      return mergeDateIntoReferenceDate(adapter, date, sections, referenceDate, false);\n    };\n    const newValue = fieldValueManager.parseValueStr(valueStr, state.referenceValue, parseDateStr);\n    publishValue(newValue);\n  };\n  const cleanActiveDateSectionsIfValueNullTimeout = useTimeout();\n  const updateSectionValue = ({\n    section,\n    newSectionValue,\n    shouldGoToNextSection\n  }) => {\n    updateSectionValueOnNextInvalidDateTimeout.clear();\n    cleanActiveDateSectionsIfValueNullTimeout.clear();\n    const activeDate = fieldValueManager.getDateFromSection(value, section);\n\n    /**\n     * Decide which section should be focused\n     */\n    if (shouldGoToNextSection && activeSectionIndex < state.sections.length - 1) {\n      setSelectedSections(activeSectionIndex + 1);\n    }\n\n    /**\n     * Try to build a valid date from the new section value\n     */\n    const newSections = setSectionValue(activeSectionIndex, newSectionValue);\n    const newActiveDateSections = fieldValueManager.getDateSectionsFromValue(newSections, section);\n    const newActiveDate = getDateFromDateSections(adapter, newActiveDateSections, localizedDigits);\n\n    /**\n     * If the new date is valid,\n     * Then we merge the value of the modified sections into the reference date.\n     * This makes sure that we don't lose some information of the initial date (like the time on a date field).\n     */\n    if (adapter.isValid(newActiveDate)) {\n      const mergedDate = mergeDateIntoReferenceDate(adapter, newActiveDate, newActiveDateSections, fieldValueManager.getDateFromSection(state.referenceValue, section), true);\n      if (activeDate == null) {\n        cleanActiveDateSectionsIfValueNullTimeout.start(0, () => {\n          if (valueRef.current === value) {\n            setState(prevState => _extends({}, prevState, {\n              sections: fieldValueManager.clearDateSections(state.sections, section),\n              tempValueStrAndroid: null\n            }));\n          }\n        });\n      }\n      return publishValue(fieldValueManager.updateDateInValue(value, section, mergedDate));\n    }\n\n    /**\n     * If all the sections are filled but the date is invalid and the previous date is valid or null,\n     * Then we publish an invalid date.\n     */\n    if (newActiveDateSections.every(sectionBis => sectionBis.value !== '') && (activeDate == null || adapter.isValid(activeDate))) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, newActiveDate));\n    }\n\n    /**\n     * If the previous date is not null,\n     * Then we publish the date as `null`.\n     */\n    if (activeDate != null) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, null));\n    }\n\n    /**\n     * If the previous date is already null,\n     * Then we don't publish the date and we update the sections.\n     */\n    return setState(prevState => _extends({}, prevState, {\n      sections: newSections,\n      tempValueStrAndroid: null\n    }));\n  };\n  const setTempAndroidValueStr = tempValueStrAndroid => setState(prevState => _extends({}, prevState, {\n    tempValueStrAndroid\n  }));\n  const setCharacterQuery = useEventCallback(newCharacterQuery => {\n    setState(prevState => _extends({}, prevState, {\n      characterQuery: newCharacterQuery\n    }));\n  });\n\n  // If `prop.value` changes, we update the state to reflect the new value\n  if (value !== state.lastExternalValue) {\n    let sections;\n    if (sectionToUpdateOnNextInvalidDateRef.current != null && !adapter.isValid(fieldValueManager.getDateFromSection(value, state.sections[sectionToUpdateOnNextInvalidDateRef.current.sectionIndex]))) {\n      sections = setSectionValue(sectionToUpdateOnNextInvalidDateRef.current.sectionIndex, sectionToUpdateOnNextInvalidDateRef.current.value);\n    } else {\n      sections = getSectionsFromValue(value);\n    }\n    setState(prevState => _extends({}, prevState, {\n      lastExternalValue: value,\n      sections,\n      sectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      referenceValue: fieldValueManager.updateReferenceValue(adapter, value, prevState.referenceValue),\n      tempValueStrAndroid: null\n    }));\n  }\n  if (isRtl !== state.lastSectionsDependencies.isRtl || format !== state.lastSectionsDependencies.format || adapter.locale !== state.lastSectionsDependencies.locale) {\n    const sections = getSectionsFromValue(value);\n    validateSections(sections, valueType);\n    setState(prevState => _extends({}, prevState, {\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      sections,\n      tempValueStrAndroid: null,\n      characterQuery: null\n    }));\n  }\n  if (state.characterQuery != null && !error && activeSectionIndex == null) {\n    setCharacterQuery(null);\n  }\n  if (state.characterQuery != null && state.sections[state.characterQuery.sectionIndex]?.type !== state.characterQuery.sectionType) {\n    setCharacterQuery(null);\n  }\n  React.useEffect(() => {\n    if (sectionToUpdateOnNextInvalidDateRef.current != null) {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    }\n  });\n  const cleanCharacterQueryTimeout = useTimeout();\n  React.useEffect(() => {\n    if (state.characterQuery != null) {\n      cleanCharacterQueryTimeout.start(QUERY_LIFE_DURATION_MS, () => setCharacterQuery(null));\n    }\n    return () => {};\n  }, [state.characterQuery, setCharacterQuery, cleanCharacterQueryTimeout]);\n\n  // If `tempValueStrAndroid` is still defined for some section when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && activeSectionIndex != null) {\n      clearActiveSection();\n    }\n  }, [state.sections]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  return {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    sectionsValueBoundaries,\n    state,\n    timezone,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  };\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,gBAAgB,EAAEC,qBAAqB,QAAQ,yBAAyB;AACjF,SAASC,0BAA0B,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,qBAAqB;AAC9L,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,yBAAyB,QAAQ,wCAAwC;AAClF,MAAMC,sBAAsB,GAAG,IAAI;AACnC,OAAO,MAAMC,aAAa,GAAGC,UAAU,IAAI;EACzC,MAAMC,OAAO,GAAGhB,gBAAgB,CAAC,CAAC;EAClC,MAAMiB,YAAY,GAAGhB,qBAAqB,CAAC,CAAC;EAC5C,MAAMiB,KAAK,GAAGnB,MAAM,CAAC,CAAC;EACtB,MAAM;IACJoB,OAAO,EAAE;MACPC,SAAS;MACTC,SAAS;MACTC,qBAAqB,EAAEC,YAAY;MACnCC,0BAA0B,EAAEC;IAC9B,CAAC;IACDC,yBAAyB;IACzBA,yBAAyB,EAAE;MACzBC,KAAK,EAAEC,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,QAAQ;MACRC,MAAM;MACNC,aAAa,GAAG,OAAO;MACvBC,gBAAgB,EAAEC,oBAAoB;MACtCC,wBAAwB;MACxBC,yBAAyB,GAAG,KAAK;MACjCC,QAAQ,EAAEC,YAAY;MACtBC,iCAAiC,GAAG;IACtC,CAAC;IACDC,cAAc,EAAE;MACdC,KAAK,EAAEC;IACT;EACF,CAAC,GAAG7B,UAAU;EACd,MAAM;IACJY,KAAK;IACLkB,iBAAiB;IACjBN;EACF,CAAC,GAAG5B,kBAAkB,CAAC;IACrBmC,IAAI,EAAE,mBAAmB;IACzBP,QAAQ,EAAEC,YAAY;IACtBb,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZC,aAAa,EAAEC,iBAAiB;IAChCC,QAAQ;IACRT;EACF,CAAC,CAAC;EACF,MAAMwB,QAAQ,GAAGpD,KAAK,CAACqD,MAAM,CAACrB,KAAK,CAAC;EACpChC,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpBF,QAAQ,CAACG,OAAO,GAAGvB,KAAK;EAC1B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,MAAM;IACJwB;EACF,CAAC,GAAGzC,aAAa,CAAC;IAChB0C,KAAK,EAAE1B,yBAAyB;IAChCN,SAAS;IACTmB,QAAQ;IACRZ,KAAK;IACL0B,OAAO,EAAE3B,yBAAyB,CAAC2B;EACrC,CAAC,CAAC;EACF,MAAMV,KAAK,GAAGhD,KAAK,CAAC2D,OAAO,CAAC,MAAM;IAChC;IACA;IACA,IAAIV,SAAS,KAAKW,SAAS,EAAE;MAC3B,OAAOX,SAAS;IAClB;IACA,OAAOO,kBAAkB;EAC3B,CAAC,EAAE,CAACA,kBAAkB,EAAEP,SAAS,CAAC,CAAC;EACnC,MAAMY,eAAe,GAAG7D,KAAK,CAAC2D,OAAO,CAAC,MAAM/C,kBAAkB,CAACS,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACnF,MAAMyC,uBAAuB,GAAG9D,KAAK,CAAC2D,OAAO,CAAC,MAAMnD,qBAAqB,CAACa,OAAO,EAAEwC,eAAe,EAAEjB,QAAQ,CAAC,EAAE,CAACvB,OAAO,EAAEwC,eAAe,EAAEjB,QAAQ,CAAC,CAAC;EACpJ,MAAMmB,oBAAoB,GAAG/D,KAAK,CAACgE,WAAW,CAACC,cAAc,IAAInC,iBAAiB,CAACiC,oBAAoB,CAACE,cAAc,EAAEC,IAAI,IAAIpD,uBAAuB,CAAC;IACtJO,OAAO;IACP8C,UAAU,EAAE7C,YAAY;IACxBuC,eAAe;IACfvB,MAAM;IACN4B,IAAI;IACJ3B,aAAa;IACbI,yBAAyB;IACzBG,iCAAiC;IACjCvB;EACF,CAAC,CAAC,CAAC,EAAE,CAACO,iBAAiB,EAAEQ,MAAM,EAAEhB,YAAY,EAAEuC,eAAe,EAAEtC,KAAK,EAAEoB,yBAAyB,EAAEtB,OAAO,EAAEkB,aAAa,EAAEO,iCAAiC,CAAC,CAAC;EAC7J,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGrE,KAAK,CAACsE,QAAQ,CAAC,MAAM;IAC7C,MAAMC,QAAQ,GAAGR,oBAAoB,CAAC/B,KAAK,CAAC;IAC5CvB,gBAAgB,CAAC8D,QAAQ,EAAE7C,SAAS,CAAC;IACrC,MAAM8C,yBAAyB,GAAG;MAChCD,QAAQ;MACRE,iBAAiB,EAAEzC,KAAK;MACxB0C,wBAAwB,EAAE;QACxBpC,MAAM;QACNf,KAAK;QACLoD,MAAM,EAAEtD,OAAO,CAACsD;MAClB,CAAC;MACDC,mBAAmB,EAAE,IAAI;MACzBC,cAAc,EAAE;IAClB,CAAC;IACD,MAAMC,WAAW,GAAG7D,yBAAyB,CAACsD,QAAQ,CAAC;IACvD,MAAMQ,cAAc,GAAGnD,YAAY,CAACoD,wBAAwB,CAAC;MAC3D7C,aAAa,EAAEC,iBAAiB;MAChCJ,KAAK;MACLX,OAAO;MACPoC,KAAK,EAAE1B,yBAAyB;MAChC+C,WAAW;MACXlC;IACF,CAAC,CAAC;IACF,OAAO7C,QAAQ,CAAC,CAAC,CAAC,EAAEyE,yBAAyB,EAAE;MAC7CO;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM,CAACvC,gBAAgB,EAAEyC,wBAAwB,CAAC,GAAGhF,aAAa,CAAC;IACjEiF,UAAU,EAAEzC,oBAAoB;IAChC0C,OAAO,EAAE,IAAI;IACbhC,IAAI,EAAE,UAAU;IAChBiB,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMgB,mBAAmB,GAAGC,mBAAmB,IAAI;IACjDJ,wBAAwB,CAACI,mBAAmB,CAAC;IAC7C3C,wBAAwB,GAAG2C,mBAAmB,CAAC;EACjD,CAAC;EACD,MAAMC,sBAAsB,GAAGtF,KAAK,CAAC2D,OAAO,CAAC,MAAMhD,qBAAqB,CAAC6B,gBAAgB,EAAE4B,KAAK,CAACG,QAAQ,CAAC,EAAE,CAAC/B,gBAAgB,EAAE4B,KAAK,CAACG,QAAQ,CAAC,CAAC;EAC/I,MAAMgB,kBAAkB,GAAGD,sBAAsB,KAAK,KAAK,GAAG,CAAC,GAAGA,sBAAsB;EACxF,MAAME,YAAY,GAAGxF,KAAK,CAAC2D,OAAO,CAAC,MAAM9C,eAAe,CAACuD,KAAK,CAACG,QAAQ,EAAEhD,KAAK,IAAI,CAACuB,iCAAiC,CAAC,EAAE,CAACsB,KAAK,CAACG,QAAQ,EAAEhD,KAAK,EAAEuB,iCAAiC,CAAC,CAAC;EAClL,MAAM2C,mBAAmB,GAAGzF,KAAK,CAAC2D,OAAO,CAAC,MAAMS,KAAK,CAACG,QAAQ,CAACmB,KAAK,CAACC,OAAO,IAAIA,OAAO,CAAC3D,KAAK,KAAK,EAAE,CAAC,EAAE,CAACoC,KAAK,CAACG,QAAQ,CAAC,CAAC;EACxH,MAAMqB,YAAY,GAAGC,QAAQ,IAAI;IAC/B,MAAMC,OAAO,GAAG;MACdC,eAAe,EAAEtE,SAAS,CAAC;QACzBJ,OAAO;QACPW,KAAK,EAAE6D,QAAQ;QACfjD,QAAQ;QACRa,KAAK,EAAE1B;MACT,CAAC;IACH,CAAC;IACDmB,iBAAiB,CAAC2C,QAAQ,EAAEC,OAAO,CAAC;EACtC,CAAC;EACD,MAAME,eAAe,GAAGA,CAACC,YAAY,EAAEC,eAAe,KAAK;IACzD,MAAMC,WAAW,GAAG,CAAC,GAAG/B,KAAK,CAACG,QAAQ,CAAC;IACvC4B,WAAW,CAACF,YAAY,CAAC,GAAGlG,QAAQ,CAAC,CAAC,CAAC,EAAEoG,WAAW,CAACF,YAAY,CAAC,EAAE;MAClEjE,KAAK,EAAEkE,eAAe;MACtBE,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,OAAOD,WAAW;EACpB,CAAC;EACD,MAAME,mCAAmC,GAAGrG,KAAK,CAACqD,MAAM,CAAC,IAAI,CAAC;EAC9D,MAAMiD,0CAA0C,GAAGpG,UAAU,CAAC,CAAC;EAC/D,MAAMqG,wCAAwC,GAAGL,eAAe,IAAI;IAClE,IAAIX,kBAAkB,IAAI,IAAI,EAAE;MAC9B;IACF;IACAc,mCAAmC,CAAC9C,OAAO,GAAG;MAC5C0C,YAAY,EAAEV,kBAAkB;MAChCvD,KAAK,EAAEkE;IACT,CAAC;IACDI,0CAA0C,CAACE,KAAK,CAAC,CAAC,EAAE,MAAM;MACxDH,mCAAmC,CAAC9C,OAAO,GAAG,IAAI;IACpD,CAAC,CAAC;EACJ,CAAC;EACD,MAAMkD,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI7E,YAAY,CAAC8E,cAAc,CAACrF,OAAO,EAAEW,KAAK,EAAEJ,YAAY,CAAC+E,UAAU,CAAC,EAAE;MACxEtC,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;QAC5CrC,QAAQ,EAAEqC,SAAS,CAACrC,QAAQ,CAACsC,GAAG,CAAClB,OAAO,IAAI5F,QAAQ,CAAC,CAAC,CAAC,EAAE4F,OAAO,EAAE;UAChE3D,KAAK,EAAE;QACT,CAAC,CAAC,CAAC;QACH4C,mBAAmB,EAAE,IAAI;QACzBC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLR,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;QAC5C/B,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;MACHe,YAAY,CAAChE,YAAY,CAAC+E,UAAU,CAAC;IACvC;EACF,CAAC;EACD,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIvB,kBAAkB,IAAI,IAAI,EAAE;MAC9B;IACF;IACA,MAAMwB,aAAa,GAAG3C,KAAK,CAACG,QAAQ,CAACgB,kBAAkB,CAAC;IACxD,IAAIwB,aAAa,CAAC/E,KAAK,KAAK,EAAE,EAAE;MAC9B;IACF;IACAuE,wCAAwC,CAAC,EAAE,CAAC;IAC5C,IAAIzE,iBAAiB,CAACkF,kBAAkB,CAAChF,KAAK,EAAE+E,aAAa,CAAC,KAAK,IAAI,EAAE;MACvE1C,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;QAC5CrC,QAAQ,EAAEyB,eAAe,CAACT,kBAAkB,EAAE,EAAE,CAAC;QACjDX,mBAAmB,EAAE,IAAI;QACzBC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLR,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;QAC5C/B,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;MACHe,YAAY,CAAC9D,iBAAiB,CAACmF,iBAAiB,CAACjF,KAAK,EAAE+E,aAAa,EAAE,IAAI,CAAC,CAAC;IAC/E;EACF,CAAC;EACD,MAAMG,uBAAuB,GAAGC,QAAQ,IAAI;IAC1C,MAAMC,YAAY,GAAGA,CAACC,OAAO,EAAElF,aAAa,KAAK;MAC/C,MAAM+B,IAAI,GAAG7C,OAAO,CAACiG,KAAK,CAACD,OAAO,EAAE/E,MAAM,CAAC;MAC3C,IAAI,CAACjB,OAAO,CAACkG,OAAO,CAACrD,IAAI,CAAC,EAAE;QAC1B,OAAO,IAAI;MACb;MACA,MAAMK,QAAQ,GAAGzD,uBAAuB,CAAC;QACvCO,OAAO;QACP8C,UAAU,EAAE7C,YAAY;QACxBuC,eAAe;QACfvB,MAAM;QACN4B,IAAI;QACJ3B,aAAa;QACbI,yBAAyB;QACzBG,iCAAiC;QACjCvB;MACF,CAAC,CAAC;MACF,OAAOhB,0BAA0B,CAACc,OAAO,EAAE6C,IAAI,EAAEK,QAAQ,EAAEpC,aAAa,EAAE,KAAK,CAAC;IAClF,CAAC;IACD,MAAM0D,QAAQ,GAAG/D,iBAAiB,CAAC0F,aAAa,CAACL,QAAQ,EAAE/C,KAAK,CAACW,cAAc,EAAEqC,YAAY,CAAC;IAC9FxB,YAAY,CAACC,QAAQ,CAAC;EACxB,CAAC;EACD,MAAM4B,yCAAyC,GAAGvH,UAAU,CAAC,CAAC;EAC9D,MAAMwH,kBAAkB,GAAGA,CAAC;IAC1B/B,OAAO;IACPO,eAAe;IACfyB;EACF,CAAC,KAAK;IACJrB,0CAA0C,CAACsB,KAAK,CAAC,CAAC;IAClDH,yCAAyC,CAACG,KAAK,CAAC,CAAC;IACjD,MAAMC,UAAU,GAAG/F,iBAAiB,CAACkF,kBAAkB,CAAChF,KAAK,EAAE2D,OAAO,CAAC;;IAEvE;AACJ;AACA;IACI,IAAIgC,qBAAqB,IAAIpC,kBAAkB,GAAGnB,KAAK,CAACG,QAAQ,CAACuD,MAAM,GAAG,CAAC,EAAE;MAC3E1C,mBAAmB,CAACG,kBAAkB,GAAG,CAAC,CAAC;IAC7C;;IAEA;AACJ;AACA;IACI,MAAMY,WAAW,GAAGH,eAAe,CAACT,kBAAkB,EAAEW,eAAe,CAAC;IACxE,MAAM6B,qBAAqB,GAAGjG,iBAAiB,CAACkG,wBAAwB,CAAC7B,WAAW,EAAER,OAAO,CAAC;IAC9F,MAAMsC,aAAa,GAAGvH,uBAAuB,CAACW,OAAO,EAAE0G,qBAAqB,EAAElE,eAAe,CAAC;;IAE9F;AACJ;AACA;AACA;AACA;IACI,IAAIxC,OAAO,CAACkG,OAAO,CAACU,aAAa,CAAC,EAAE;MAClC,MAAMC,UAAU,GAAG3H,0BAA0B,CAACc,OAAO,EAAE4G,aAAa,EAAEF,qBAAqB,EAAEjG,iBAAiB,CAACkF,kBAAkB,CAAC5C,KAAK,CAACW,cAAc,EAAEY,OAAO,CAAC,EAAE,IAAI,CAAC;MACvK,IAAIkC,UAAU,IAAI,IAAI,EAAE;QACtBJ,yCAAyC,CAACjB,KAAK,CAAC,CAAC,EAAE,MAAM;UACvD,IAAIpD,QAAQ,CAACG,OAAO,KAAKvB,KAAK,EAAE;YAC9BqC,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;cAC5CrC,QAAQ,EAAEzC,iBAAiB,CAACqG,iBAAiB,CAAC/D,KAAK,CAACG,QAAQ,EAAEoB,OAAO,CAAC;cACtEf,mBAAmB,EAAE;YACvB,CAAC,CAAC,CAAC;UACL;QACF,CAAC,CAAC;MACJ;MACA,OAAOgB,YAAY,CAAC9D,iBAAiB,CAACmF,iBAAiB,CAACjF,KAAK,EAAE2D,OAAO,EAAEuC,UAAU,CAAC,CAAC;IACtF;;IAEA;AACJ;AACA;AACA;IACI,IAAIH,qBAAqB,CAACrC,KAAK,CAAC0C,UAAU,IAAIA,UAAU,CAACpG,KAAK,KAAK,EAAE,CAAC,KAAK6F,UAAU,IAAI,IAAI,IAAIxG,OAAO,CAACkG,OAAO,CAACM,UAAU,CAAC,CAAC,EAAE;MAC7HtB,wCAAwC,CAACL,eAAe,CAAC;MACzD,OAAON,YAAY,CAAC9D,iBAAiB,CAACmF,iBAAiB,CAACjF,KAAK,EAAE2D,OAAO,EAAEsC,aAAa,CAAC,CAAC;IACzF;;IAEA;AACJ;AACA;AACA;IACI,IAAIJ,UAAU,IAAI,IAAI,EAAE;MACtBtB,wCAAwC,CAACL,eAAe,CAAC;MACzD,OAAON,YAAY,CAAC9D,iBAAiB,CAACmF,iBAAiB,CAACjF,KAAK,EAAE2D,OAAO,EAAE,IAAI,CAAC,CAAC;IAChF;;IAEA;AACJ;AACA;AACA;IACI,OAAOtB,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;MACnDrC,QAAQ,EAAE4B,WAAW;MACrBvB,mBAAmB,EAAE;IACvB,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMyD,sBAAsB,GAAGzD,mBAAmB,IAAIP,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;IAClGhC;EACF,CAAC,CAAC,CAAC;EACH,MAAM0D,iBAAiB,GAAGnI,gBAAgB,CAACoI,iBAAiB,IAAI;IAC9DlE,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;MAC5C/B,cAAc,EAAE0D;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;;EAEF;EACA,IAAIvG,KAAK,KAAKoC,KAAK,CAACK,iBAAiB,EAAE;IACrC,IAAIF,QAAQ;IACZ,IAAI8B,mCAAmC,CAAC9C,OAAO,IAAI,IAAI,IAAI,CAAClC,OAAO,CAACkG,OAAO,CAACzF,iBAAiB,CAACkF,kBAAkB,CAAChF,KAAK,EAAEoC,KAAK,CAACG,QAAQ,CAAC8B,mCAAmC,CAAC9C,OAAO,CAAC0C,YAAY,CAAC,CAAC,CAAC,EAAE;MAClM1B,QAAQ,GAAGyB,eAAe,CAACK,mCAAmC,CAAC9C,OAAO,CAAC0C,YAAY,EAAEI,mCAAmC,CAAC9C,OAAO,CAACvB,KAAK,CAAC;IACzI,CAAC,MAAM;MACLuC,QAAQ,GAAGR,oBAAoB,CAAC/B,KAAK,CAAC;IACxC;IACAqC,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;MAC5CnC,iBAAiB,EAAEzC,KAAK;MACxBuC,QAAQ;MACRiE,oBAAoB,EAAE;QACpBlG,MAAM;QACNf,KAAK;QACLoD,MAAM,EAAEtD,OAAO,CAACsD;MAClB,CAAC;MACDI,cAAc,EAAEjD,iBAAiB,CAAC2G,oBAAoB,CAACpH,OAAO,EAAEW,KAAK,EAAE4E,SAAS,CAAC7B,cAAc,CAAC;MAChGH,mBAAmB,EAAE;IACvB,CAAC,CAAC,CAAC;EACL;EACA,IAAIrD,KAAK,KAAK6C,KAAK,CAACM,wBAAwB,CAACnD,KAAK,IAAIe,MAAM,KAAK8B,KAAK,CAACM,wBAAwB,CAACpC,MAAM,IAAIjB,OAAO,CAACsD,MAAM,KAAKP,KAAK,CAACM,wBAAwB,CAACC,MAAM,EAAE;IAClK,MAAMJ,QAAQ,GAAGR,oBAAoB,CAAC/B,KAAK,CAAC;IAC5CvB,gBAAgB,CAAC8D,QAAQ,EAAE7C,SAAS,CAAC;IACrC2C,QAAQ,CAACuC,SAAS,IAAI7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,SAAS,EAAE;MAC5ClC,wBAAwB,EAAE;QACxBpC,MAAM;QACNf,KAAK;QACLoD,MAAM,EAAEtD,OAAO,CAACsD;MAClB,CAAC;MACDJ,QAAQ;MACRK,mBAAmB,EAAE,IAAI;MACzBC,cAAc,EAAE;IAClB,CAAC,CAAC,CAAC;EACL;EACA,IAAIT,KAAK,CAACS,cAAc,IAAI,IAAI,IAAI,CAAC7B,KAAK,IAAIuC,kBAAkB,IAAI,IAAI,EAAE;IACxE+C,iBAAiB,CAAC,IAAI,CAAC;EACzB;EACA,IAAIlE,KAAK,CAACS,cAAc,IAAI,IAAI,IAAIT,KAAK,CAACG,QAAQ,CAACH,KAAK,CAACS,cAAc,CAACoB,YAAY,CAAC,EAAEyC,IAAI,KAAKtE,KAAK,CAACS,cAAc,CAAC8D,WAAW,EAAE;IAChIL,iBAAiB,CAAC,IAAI,CAAC;EACzB;EACAtI,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpB,IAAI+C,mCAAmC,CAAC9C,OAAO,IAAI,IAAI,EAAE;MACvD8C,mCAAmC,CAAC9C,OAAO,GAAG,IAAI;IACpD;EACF,CAAC,CAAC;EACF,MAAMqF,0BAA0B,GAAG1I,UAAU,CAAC,CAAC;EAC/CF,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpB,IAAIc,KAAK,CAACS,cAAc,IAAI,IAAI,EAAE;MAChC+D,0BAA0B,CAACpC,KAAK,CAACtF,sBAAsB,EAAE,MAAMoH,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACzF;IACA,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,CAAClE,KAAK,CAACS,cAAc,EAAEyD,iBAAiB,EAAEM,0BAA0B,CAAC,CAAC;;EAEzE;EACA;EACA;EACA;EACA5I,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpB,IAAIc,KAAK,CAACQ,mBAAmB,IAAI,IAAI,IAAIW,kBAAkB,IAAI,IAAI,EAAE;MACnEuB,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAAC1C,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtB,OAAO;IACL;IACAgB,kBAAkB;IAClBE,mBAAmB;IACnBzC,KAAK;IACLa,eAAe;IACfyB,sBAAsB;IACtBE,YAAY;IACZ1B,uBAAuB;IACvBM,KAAK;IACLxB,QAAQ;IACRZ,KAAK;IACL;IACAyE,UAAU;IACVK,kBAAkB;IAClBwB,iBAAiB;IACjBlD,mBAAmB;IACnBiD,sBAAsB;IACtBX,kBAAkB;IAClBR,uBAAuB;IACvB;IACAnD;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}