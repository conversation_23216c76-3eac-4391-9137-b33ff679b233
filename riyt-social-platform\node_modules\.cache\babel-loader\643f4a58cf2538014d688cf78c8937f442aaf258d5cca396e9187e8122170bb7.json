{"ast": null, "code": "import { isoSpecifier } from \"./isoFormat.js\";\nimport { utcParse } from \"./defaultLocale.js\";\nfunction parseIsoNative(string) {\n  var date = new Date(string);\n  return isNaN(date) ? null : date;\n}\nvar parseIso = +new Date(\"2000-01-01T00:00:00.000Z\") ? parseIsoNative : utcParse(isoSpecifier);\nexport default parseIso;", "map": {"version": 3, "names": ["isoSpecifier", "utcParse", "parseIsoNative", "string", "date", "Date", "isNaN", "parseIso"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-time-format/src/isoParse.js"], "sourcesContent": ["import {isoSpecifier} from \"./isoFormat.js\";\nimport {utcParse} from \"./defaultLocale.js\";\n\nfunction parseIsoNative(string) {\n  var date = new Date(string);\n  return isNaN(date) ? null : date;\n}\n\nvar parseIso = +new Date(\"2000-01-01T00:00:00.000Z\")\n    ? parseIsoNative\n    : utcParse(isoSpecifier);\n\nexport default parseIso;\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,QAAQ,QAAO,oBAAoB;AAE3C,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAACF,MAAM,CAAC;EAC3B,OAAOG,KAAK,CAACF,IAAI,CAAC,GAAG,IAAI,GAAGA,IAAI;AAClC;AAEA,IAAIG,QAAQ,GAAG,CAAC,IAAIF,IAAI,CAAC,0BAA0B,CAAC,GAC9CH,cAAc,GACdD,QAAQ,CAACD,YAAY,CAAC;AAE5B,eAAeO,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}