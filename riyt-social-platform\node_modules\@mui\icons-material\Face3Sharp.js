"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "9",
  cy: "13",
  r: "1.25"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "15",
  cy: "13",
  r: "1.25"
}, "1"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M22.91 11.96c-.54-5.93-5.75-10.41-11.8-9.92-5.38.42-9.56 4.9-10.05 10.28L0 24h24zM4.54 9.13c.87.55 1.89.87 2.96.87 1.86 0 3.5-.93 4.5-2.35C13 9.07 14.64 10 16.5 10c1.07 0 2.09-.32 2.96-.87.34.89.54 1.86.54 2.87 0 4.41-3.59 8-8 8s-8-3.59-8-8c0-1.01.2-1.98.54-2.87"
}, "2")], 'Face3Sharp');