{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useControlledValue } from \"../../useControlledValue.js\";\nimport { usePickerAdapter } from \"../../../../hooks/usePickerAdapter.js\";\nimport { useValidation } from \"../../../../validation/index.js\";\nexport function useValueAndOpenStates(parameters) {\n  const {\n    props,\n    valueManager,\n    validator\n  } = parameters;\n  const {\n    value: valueProp,\n    defaultValue: defaultValueProp,\n    onChange,\n    referenceDate,\n    timezone: timezoneProp,\n    onAccept,\n    closeOnSelect,\n    open: openProp,\n    onOpen,\n    onClose\n  } = props;\n  const {\n    current: defaultValue\n  } = React.useRef(defaultValueProp);\n  const {\n    current: isValueControlled\n  } = React.useRef(valueProp !== undefined);\n  const {\n    current: isOpenControlled\n  } = React.useRef(openProp !== undefined);\n  const adapter = usePickerAdapter();\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.renderInput != null) {\n      warnOnce(['MUI X: The `renderInput` prop has been removed in version 6.0 of the Date and Time Pickers.', 'You can replace it with the `textField` component slot in most cases.', 'For more information, please have a look at the migration guide (https://mui.com/x/migration/migration-pickers-v5/#input-renderer-required-in-v5).']);\n    }\n  }\n\n  /* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isValueControlled !== (valueProp !== undefined)) {\n        console.error([`MUI X: A component is changing the ${isValueControlled ? '' : 'un'}controlled value of a Picker to be ${isValueControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled value` + 'for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [valueProp]);\n    React.useEffect(() => {\n      if (!isValueControlled && defaultValue !== defaultValueProp) {\n        console.error([`MUI X: A component is changing the defaultValue of an uncontrolled Picker after being initialized. ` + `To suppress this warning opt to use a controlled value.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultValue)]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n\n  const {\n    timezone,\n    value,\n    handleValueChange\n  } = useControlledValue({\n    name: 'a picker component',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate,\n    onChange,\n    valueManager\n  });\n  const [state, setState] = React.useState(() => ({\n    open: false,\n    lastExternalValue: value,\n    clockShallowValue: undefined,\n    lastCommittedValue: value,\n    hasBeenModifiedSinceMount: false\n  }));\n  const {\n    getValidationErrorForNewValue\n  } = useValidation({\n    props,\n    validator,\n    timezone,\n    value,\n    onError: props.onError\n  });\n  const setOpen = useEventCallback(action => {\n    const newOpen = typeof action === 'function' ? action(state.open) : action;\n    if (!isOpenControlled) {\n      setState(prevState => _extends({}, prevState, {\n        open: newOpen\n      }));\n    }\n    if (newOpen && onOpen) {\n      onOpen();\n    }\n    if (!newOpen) {\n      onClose?.();\n    }\n  });\n  const setValue = useEventCallback((newValue, options) => {\n    const {\n      changeImportance = 'accept',\n      skipPublicationIfPristine = false,\n      validationError,\n      shortcut,\n      shouldClose = changeImportance === 'accept'\n    } = options ?? {};\n    let shouldFireOnChange;\n    let shouldFireOnAccept;\n    if (!skipPublicationIfPristine && !isValueControlled && !state.hasBeenModifiedSinceMount) {\n      // If the value is not controlled and the value has never been modified before,\n      // Then clicking on any value (including the one equal to `defaultValue`) should call `onChange` and `onAccept`\n      shouldFireOnChange = true;\n      shouldFireOnAccept = changeImportance === 'accept';\n    } else {\n      shouldFireOnChange = !valueManager.areValuesEqual(adapter, newValue, value);\n      shouldFireOnAccept = changeImportance === 'accept' && !valueManager.areValuesEqual(adapter, newValue, state.lastCommittedValue);\n    }\n    setState(prevState => _extends({}, prevState, {\n      // We reset the shallow value whenever we fire onChange.\n      clockShallowValue: shouldFireOnChange ? undefined : prevState.clockShallowValue,\n      lastCommittedValue: shouldFireOnAccept ? newValue : prevState.lastCommittedValue,\n      hasBeenModifiedSinceMount: true\n    }));\n    let cachedContext = null;\n    const getContext = () => {\n      if (!cachedContext) {\n        cachedContext = {\n          validationError: validationError == null ? getValidationErrorForNewValue(newValue) : validationError\n        };\n        if (shortcut) {\n          cachedContext.shortcut = shortcut;\n        }\n      }\n      return cachedContext;\n    };\n    if (shouldFireOnChange) {\n      handleValueChange(newValue, getContext());\n    }\n    if (shouldFireOnAccept && onAccept) {\n      onAccept(newValue, getContext());\n    }\n    if (shouldClose) {\n      setOpen(false);\n    }\n  });\n\n  // If `prop.value` changes, we update the state to reflect the new value\n  if (value !== state.lastExternalValue) {\n    setState(prevState => _extends({}, prevState, {\n      lastExternalValue: value,\n      clockShallowValue: undefined,\n      hasBeenModifiedSinceMount: true\n    }));\n  }\n  const setValueFromView = useEventCallback((newValue, selectionState = 'partial') => {\n    // TODO: Expose a new method (private?) like `setView` that only updates the clock shallow value.\n    if (selectionState === 'shallow') {\n      setState(prev => _extends({}, prev, {\n        clockShallowValue: newValue,\n        hasBeenModifiedSinceMount: true\n      }));\n      return;\n    }\n    setValue(newValue, {\n      changeImportance: selectionState === 'finish' && closeOnSelect ? 'accept' : 'set'\n    });\n  });\n\n  // It is required to update inner state in useEffect in order to avoid situation when\n  // Our component is not mounted yet, but `open` state is set to `true` (for example initially opened)\n  React.useEffect(() => {\n    if (isOpenControlled) {\n      if (openProp === undefined) {\n        throw new Error('You must not mix controlling and uncontrolled mode for `open` prop');\n      }\n      setState(prevState => _extends({}, prevState, {\n        open: openProp\n      }));\n    }\n  }, [isOpenControlled, openProp]);\n  const viewValue = React.useMemo(() => valueManager.cleanValue(adapter, state.clockShallowValue === undefined ? value : state.clockShallowValue), [adapter, valueManager, state.clockShallowValue, value]);\n  return {\n    timezone,\n    state,\n    setValue,\n    setValueFromView,\n    setOpen,\n    value,\n    viewValue\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "warnOnce", "useEventCallback", "useControlledValue", "usePickerAdapter", "useValidation", "useValueAndOpenStates", "parameters", "props", "valueManager", "validator", "value", "valueProp", "defaultValue", "defaultValueProp", "onChange", "referenceDate", "timezone", "timezoneProp", "onAccept", "closeOnSelect", "open", "openProp", "onOpen", "onClose", "current", "useRef", "isValueControlled", "undefined", "isOpenControlled", "adapter", "process", "env", "NODE_ENV", "renderInput", "useEffect", "console", "error", "join", "JSON", "stringify", "handleValueChange", "name", "state", "setState", "useState", "lastExternalValue", "clockShallowValue", "lastCommittedValue", "hasBeenModifiedSinceMount", "getValidationErrorForNewValue", "onError", "<PERSON><PERSON><PERSON>", "action", "newOpen", "prevState", "setValue", "newValue", "options", "changeImportance", "skipPublicationIfPristine", "validationError", "shortcut", "shouldClose", "shouldFireOnChange", "shouldFireOnAccept", "areValuesEqual", "cachedContext", "getContext", "setV<PERSON>ueFromView", "selectionState", "prev", "Error", "viewValue", "useMemo", "cleanValue"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/@mui/x-date-pickers/esm/internals/hooks/usePicker/hooks/useValueAndOpenStates.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useControlledValue } from \"../../useControlledValue.js\";\nimport { usePickerAdapter } from \"../../../../hooks/usePickerAdapter.js\";\nimport { useValidation } from \"../../../../validation/index.js\";\nexport function useValueAndOpenStates(parameters) {\n  const {\n    props,\n    valueManager,\n    validator\n  } = parameters;\n  const {\n    value: valueProp,\n    defaultValue: defaultValueProp,\n    onChange,\n    referenceDate,\n    timezone: timezoneProp,\n    onAccept,\n    closeOnSelect,\n    open: openProp,\n    onOpen,\n    onClose\n  } = props;\n  const {\n    current: defaultValue\n  } = React.useRef(defaultValueProp);\n  const {\n    current: isValueControlled\n  } = React.useRef(valueProp !== undefined);\n  const {\n    current: isOpenControlled\n  } = React.useRef(openProp !== undefined);\n  const adapter = usePickerAdapter();\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.renderInput != null) {\n      warnOnce(['MUI X: The `renderInput` prop has been removed in version 6.0 of the Date and Time Pickers.', 'You can replace it with the `textField` component slot in most cases.', 'For more information, please have a look at the migration guide (https://mui.com/x/migration/migration-pickers-v5/#input-renderer-required-in-v5).']);\n    }\n  }\n\n  /* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isValueControlled !== (valueProp !== undefined)) {\n        console.error([`MUI X: A component is changing the ${isValueControlled ? '' : 'un'}controlled value of a Picker to be ${isValueControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled value` + 'for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [valueProp]);\n    React.useEffect(() => {\n      if (!isValueControlled && defaultValue !== defaultValueProp) {\n        console.error([`MUI X: A component is changing the defaultValue of an uncontrolled Picker after being initialized. ` + `To suppress this warning opt to use a controlled value.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultValue)]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n\n  const {\n    timezone,\n    value,\n    handleValueChange\n  } = useControlledValue({\n    name: 'a picker component',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate,\n    onChange,\n    valueManager\n  });\n  const [state, setState] = React.useState(() => ({\n    open: false,\n    lastExternalValue: value,\n    clockShallowValue: undefined,\n    lastCommittedValue: value,\n    hasBeenModifiedSinceMount: false\n  }));\n  const {\n    getValidationErrorForNewValue\n  } = useValidation({\n    props,\n    validator,\n    timezone,\n    value,\n    onError: props.onError\n  });\n  const setOpen = useEventCallback(action => {\n    const newOpen = typeof action === 'function' ? action(state.open) : action;\n    if (!isOpenControlled) {\n      setState(prevState => _extends({}, prevState, {\n        open: newOpen\n      }));\n    }\n    if (newOpen && onOpen) {\n      onOpen();\n    }\n    if (!newOpen) {\n      onClose?.();\n    }\n  });\n  const setValue = useEventCallback((newValue, options) => {\n    const {\n      changeImportance = 'accept',\n      skipPublicationIfPristine = false,\n      validationError,\n      shortcut,\n      shouldClose = changeImportance === 'accept'\n    } = options ?? {};\n    let shouldFireOnChange;\n    let shouldFireOnAccept;\n    if (!skipPublicationIfPristine && !isValueControlled && !state.hasBeenModifiedSinceMount) {\n      // If the value is not controlled and the value has never been modified before,\n      // Then clicking on any value (including the one equal to `defaultValue`) should call `onChange` and `onAccept`\n      shouldFireOnChange = true;\n      shouldFireOnAccept = changeImportance === 'accept';\n    } else {\n      shouldFireOnChange = !valueManager.areValuesEqual(adapter, newValue, value);\n      shouldFireOnAccept = changeImportance === 'accept' && !valueManager.areValuesEqual(adapter, newValue, state.lastCommittedValue);\n    }\n    setState(prevState => _extends({}, prevState, {\n      // We reset the shallow value whenever we fire onChange.\n      clockShallowValue: shouldFireOnChange ? undefined : prevState.clockShallowValue,\n      lastCommittedValue: shouldFireOnAccept ? newValue : prevState.lastCommittedValue,\n      hasBeenModifiedSinceMount: true\n    }));\n    let cachedContext = null;\n    const getContext = () => {\n      if (!cachedContext) {\n        cachedContext = {\n          validationError: validationError == null ? getValidationErrorForNewValue(newValue) : validationError\n        };\n        if (shortcut) {\n          cachedContext.shortcut = shortcut;\n        }\n      }\n      return cachedContext;\n    };\n    if (shouldFireOnChange) {\n      handleValueChange(newValue, getContext());\n    }\n    if (shouldFireOnAccept && onAccept) {\n      onAccept(newValue, getContext());\n    }\n    if (shouldClose) {\n      setOpen(false);\n    }\n  });\n\n  // If `prop.value` changes, we update the state to reflect the new value\n  if (value !== state.lastExternalValue) {\n    setState(prevState => _extends({}, prevState, {\n      lastExternalValue: value,\n      clockShallowValue: undefined,\n      hasBeenModifiedSinceMount: true\n    }));\n  }\n  const setValueFromView = useEventCallback((newValue, selectionState = 'partial') => {\n    // TODO: Expose a new method (private?) like `setView` that only updates the clock shallow value.\n    if (selectionState === 'shallow') {\n      setState(prev => _extends({}, prev, {\n        clockShallowValue: newValue,\n        hasBeenModifiedSinceMount: true\n      }));\n      return;\n    }\n    setValue(newValue, {\n      changeImportance: selectionState === 'finish' && closeOnSelect ? 'accept' : 'set'\n    });\n  });\n\n  // It is required to update inner state in useEffect in order to avoid situation when\n  // Our component is not mounted yet, but `open` state is set to `true` (for example initially opened)\n  React.useEffect(() => {\n    if (isOpenControlled) {\n      if (openProp === undefined) {\n        throw new Error('You must not mix controlling and uncontrolled mode for `open` prop');\n      }\n      setState(prevState => _extends({}, prevState, {\n        open: openProp\n      }));\n    }\n  }, [isOpenControlled, openProp]);\n  const viewValue = React.useMemo(() => valueManager.cleanValue(adapter, state.clockShallowValue === undefined ? value : state.clockShallowValue), [adapter, valueManager, state.clockShallowValue, value]);\n  return {\n    timezone,\n    state,\n    setValue,\n    setValueFromView,\n    setOpen,\n    value,\n    viewValue\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,OAAO,SAASC,qBAAqBA,CAACC,UAAU,EAAE;EAChD,MAAM;IACJC,KAAK;IACLC,YAAY;IACZC;EACF,CAAC,GAAGH,UAAU;EACd,MAAM;IACJI,KAAK,EAAEC,SAAS;IAChBC,YAAY,EAAEC,gBAAgB;IAC9BC,QAAQ;IACRC,aAAa;IACbC,QAAQ,EAAEC,YAAY;IACtBC,QAAQ;IACRC,aAAa;IACbC,IAAI,EAAEC,QAAQ;IACdC,MAAM;IACNC;EACF,CAAC,GAAGhB,KAAK;EACT,MAAM;IACJiB,OAAO,EAAEZ;EACX,CAAC,GAAGb,KAAK,CAAC0B,MAAM,CAACZ,gBAAgB,CAAC;EAClC,MAAM;IACJW,OAAO,EAAEE;EACX,CAAC,GAAG3B,KAAK,CAAC0B,MAAM,CAACd,SAAS,KAAKgB,SAAS,CAAC;EACzC,MAAM;IACJH,OAAO,EAAEI;EACX,CAAC,GAAG7B,KAAK,CAAC0B,MAAM,CAACJ,QAAQ,KAAKM,SAAS,CAAC;EACxC,MAAME,OAAO,GAAG1B,gBAAgB,CAAC,CAAC;EAClC,IAAI2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIzB,KAAK,CAAC0B,WAAW,IAAI,IAAI,EAAE;MAC7BjC,QAAQ,CAAC,CAAC,6FAA6F,EAAE,uEAAuE,EAAE,oJAAoJ,CAAC,CAAC;IAC1U;EACF;;EAEA;EACA,IAAI8B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCjC,KAAK,CAACmC,SAAS,CAAC,MAAM;MACpB,IAAIR,iBAAiB,MAAMf,SAAS,KAAKgB,SAAS,CAAC,EAAE;QACnDQ,OAAO,CAACC,KAAK,CAAC,CAAC,sCAAsCV,iBAAiB,GAAG,EAAE,GAAG,IAAI,sCAAsCA,iBAAiB,GAAG,IAAI,GAAG,EAAE,aAAa,EAAE,6EAA6E,EAAE,yDAAyD,GAAG,oCAAoC,EAAE,4HAA4H,EAAE,sDAAsD,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;MACxhB;IACF,CAAC,EAAE,CAAC1B,SAAS,CAAC,CAAC;IACfZ,KAAK,CAACmC,SAAS,CAAC,MAAM;MACpB,IAAI,CAACR,iBAAiB,IAAId,YAAY,KAAKC,gBAAgB,EAAE;QAC3DsB,OAAO,CAACC,KAAK,CAAC,CAAC,qGAAqG,GAAG,yDAAyD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/L;IACF,CAAC,EAAE,CAACC,IAAI,CAACC,SAAS,CAAC3B,YAAY,CAAC,CAAC,CAAC;EACpC;EACA;;EAEA,MAAM;IACJI,QAAQ;IACRN,KAAK;IACL8B;EACF,CAAC,GAAGtC,kBAAkB,CAAC;IACrBuC,IAAI,EAAE,oBAAoB;IAC1BzB,QAAQ,EAAEC,YAAY;IACtBP,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZG,aAAa;IACbD,QAAQ;IACRN;EACF,CAAC,CAAC;EACF,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,KAAK,CAAC6C,QAAQ,CAAC,OAAO;IAC9CxB,IAAI,EAAE,KAAK;IACXyB,iBAAiB,EAAEnC,KAAK;IACxBoC,iBAAiB,EAAEnB,SAAS;IAC5BoB,kBAAkB,EAAErC,KAAK;IACzBsC,yBAAyB,EAAE;EAC7B,CAAC,CAAC,CAAC;EACH,MAAM;IACJC;EACF,CAAC,GAAG7C,aAAa,CAAC;IAChBG,KAAK;IACLE,SAAS;IACTO,QAAQ;IACRN,KAAK;IACLwC,OAAO,EAAE3C,KAAK,CAAC2C;EACjB,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGlD,gBAAgB,CAACmD,MAAM,IAAI;IACzC,MAAMC,OAAO,GAAG,OAAOD,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACV,KAAK,CAACtB,IAAI,CAAC,GAAGgC,MAAM;IAC1E,IAAI,CAACxB,gBAAgB,EAAE;MACrBe,QAAQ,CAACW,SAAS,IAAIxD,QAAQ,CAAC,CAAC,CAAC,EAAEwD,SAAS,EAAE;QAC5ClC,IAAI,EAAEiC;MACR,CAAC,CAAC,CAAC;IACL;IACA,IAAIA,OAAO,IAAI/B,MAAM,EAAE;MACrBA,MAAM,CAAC,CAAC;IACV;IACA,IAAI,CAAC+B,OAAO,EAAE;MACZ9B,OAAO,GAAG,CAAC;IACb;EACF,CAAC,CAAC;EACF,MAAMgC,QAAQ,GAAGtD,gBAAgB,CAAC,CAACuD,QAAQ,EAAEC,OAAO,KAAK;IACvD,MAAM;MACJC,gBAAgB,GAAG,QAAQ;MAC3BC,yBAAyB,GAAG,KAAK;MACjCC,eAAe;MACfC,QAAQ;MACRC,WAAW,GAAGJ,gBAAgB,KAAK;IACrC,CAAC,GAAGD,OAAO,IAAI,CAAC,CAAC;IACjB,IAAIM,kBAAkB;IACtB,IAAIC,kBAAkB;IACtB,IAAI,CAACL,yBAAyB,IAAI,CAACjC,iBAAiB,IAAI,CAACgB,KAAK,CAACM,yBAAyB,EAAE;MACxF;MACA;MACAe,kBAAkB,GAAG,IAAI;MACzBC,kBAAkB,GAAGN,gBAAgB,KAAK,QAAQ;IACpD,CAAC,MAAM;MACLK,kBAAkB,GAAG,CAACvD,YAAY,CAACyD,cAAc,CAACpC,OAAO,EAAE2B,QAAQ,EAAE9C,KAAK,CAAC;MAC3EsD,kBAAkB,GAAGN,gBAAgB,KAAK,QAAQ,IAAI,CAAClD,YAAY,CAACyD,cAAc,CAACpC,OAAO,EAAE2B,QAAQ,EAAEd,KAAK,CAACK,kBAAkB,CAAC;IACjI;IACAJ,QAAQ,CAACW,SAAS,IAAIxD,QAAQ,CAAC,CAAC,CAAC,EAAEwD,SAAS,EAAE;MAC5C;MACAR,iBAAiB,EAAEiB,kBAAkB,GAAGpC,SAAS,GAAG2B,SAAS,CAACR,iBAAiB;MAC/EC,kBAAkB,EAAEiB,kBAAkB,GAAGR,QAAQ,GAAGF,SAAS,CAACP,kBAAkB;MAChFC,yBAAyB,EAAE;IAC7B,CAAC,CAAC,CAAC;IACH,IAAIkB,aAAa,GAAG,IAAI;IACxB,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI,CAACD,aAAa,EAAE;QAClBA,aAAa,GAAG;UACdN,eAAe,EAAEA,eAAe,IAAI,IAAI,GAAGX,6BAA6B,CAACO,QAAQ,CAAC,GAAGI;QACvF,CAAC;QACD,IAAIC,QAAQ,EAAE;UACZK,aAAa,CAACL,QAAQ,GAAGA,QAAQ;QACnC;MACF;MACA,OAAOK,aAAa;IACtB,CAAC;IACD,IAAIH,kBAAkB,EAAE;MACtBvB,iBAAiB,CAACgB,QAAQ,EAAEW,UAAU,CAAC,CAAC,CAAC;IAC3C;IACA,IAAIH,kBAAkB,IAAI9C,QAAQ,EAAE;MAClCA,QAAQ,CAACsC,QAAQ,EAAEW,UAAU,CAAC,CAAC,CAAC;IAClC;IACA,IAAIL,WAAW,EAAE;MACfX,OAAO,CAAC,KAAK,CAAC;IAChB;EACF,CAAC,CAAC;;EAEF;EACA,IAAIzC,KAAK,KAAKgC,KAAK,CAACG,iBAAiB,EAAE;IACrCF,QAAQ,CAACW,SAAS,IAAIxD,QAAQ,CAAC,CAAC,CAAC,EAAEwD,SAAS,EAAE;MAC5CT,iBAAiB,EAAEnC,KAAK;MACxBoC,iBAAiB,EAAEnB,SAAS;MAC5BqB,yBAAyB,EAAE;IAC7B,CAAC,CAAC,CAAC;EACL;EACA,MAAMoB,gBAAgB,GAAGnE,gBAAgB,CAAC,CAACuD,QAAQ,EAAEa,cAAc,GAAG,SAAS,KAAK;IAClF;IACA,IAAIA,cAAc,KAAK,SAAS,EAAE;MAChC1B,QAAQ,CAAC2B,IAAI,IAAIxE,QAAQ,CAAC,CAAC,CAAC,EAAEwE,IAAI,EAAE;QAClCxB,iBAAiB,EAAEU,QAAQ;QAC3BR,yBAAyB,EAAE;MAC7B,CAAC,CAAC,CAAC;MACH;IACF;IACAO,QAAQ,CAACC,QAAQ,EAAE;MACjBE,gBAAgB,EAAEW,cAAc,KAAK,QAAQ,IAAIlD,aAAa,GAAG,QAAQ,GAAG;IAC9E,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA;EACApB,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIN,gBAAgB,EAAE;MACpB,IAAIP,QAAQ,KAAKM,SAAS,EAAE;QAC1B,MAAM,IAAI4C,KAAK,CAAC,oEAAoE,CAAC;MACvF;MACA5B,QAAQ,CAACW,SAAS,IAAIxD,QAAQ,CAAC,CAAC,CAAC,EAAEwD,SAAS,EAAE;QAC5ClC,IAAI,EAAEC;MACR,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACO,gBAAgB,EAAEP,QAAQ,CAAC,CAAC;EAChC,MAAMmD,SAAS,GAAGzE,KAAK,CAAC0E,OAAO,CAAC,MAAMjE,YAAY,CAACkE,UAAU,CAAC7C,OAAO,EAAEa,KAAK,CAACI,iBAAiB,KAAKnB,SAAS,GAAGjB,KAAK,GAAGgC,KAAK,CAACI,iBAAiB,CAAC,EAAE,CAACjB,OAAO,EAAErB,YAAY,EAAEkC,KAAK,CAACI,iBAAiB,EAAEpC,KAAK,CAAC,CAAC;EACzM,OAAO;IACLM,QAAQ;IACR0B,KAAK;IACLa,QAAQ;IACRa,gBAAgB;IAChBjB,OAAO;IACPzC,KAAK;IACL8D;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}