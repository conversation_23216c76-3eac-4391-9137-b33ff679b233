{"ast": null, "code": "/**\n * Represents a single item in the ReactSmoothQueue.\n * The item can be:\n * - A number representing a delay in milliseconds.\n * - An object representing a style change\n * - A StartAnimationFunction that starts eased transition and calls different render\n *      because of course in Recharts we have to have three ways to do everything\n * - An arbitrary function to be executed\n */\n\nexport function createAnimateManager(timeoutController) {\n  var currStyle = {};\n  var handleChange = () => null;\n  var shouldStop = false;\n  var cancelTimeout = null;\n  var setStyle = _style => {\n    if (shouldStop) {\n      return;\n    }\n    if (Array.isArray(_style)) {\n      if (!_style.length) {\n        return;\n      }\n      var styles = _style;\n      var [curr, ...restStyles] = styles;\n      if (typeof curr === 'number') {\n        cancelTimeout = timeoutController.setTimeout(setStyle.bind(null, restStyles), curr);\n        return;\n      }\n      setStyle(curr);\n      cancelTimeout = timeoutController.setTimeout(setStyle.bind(null, restStyles));\n      return;\n    }\n    if (typeof _style === 'object') {\n      currStyle = _style;\n      handleChange(currStyle);\n    }\n    if (typeof _style === 'function') {\n      _style();\n    }\n  };\n  return {\n    stop: () => {\n      shouldStop = true;\n    },\n    start: style => {\n      shouldStop = false;\n      if (cancelTimeout) {\n        cancelTimeout();\n        cancelTimeout = null;\n      }\n      setStyle(style);\n    },\n    subscribe: _handleChange => {\n      handleChange = _handleChange;\n      return () => {\n        handleChange = () => null;\n      };\n    },\n    getTimeoutController: () => timeoutController\n  };\n}", "map": {"version": 3, "names": ["createAnimateManager", "timeoutController", "currStyle", "handleChange", "shouldStop", "cancelTimeout", "setStyle", "_style", "Array", "isArray", "length", "styles", "curr", "restStyles", "setTimeout", "bind", "stop", "start", "style", "subscribe", "_handleChange", "getTimeoutController"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/animation/AnimationManager.js"], "sourcesContent": ["/**\n * Represents a single item in the ReactSmoothQueue.\n * The item can be:\n * - A number representing a delay in milliseconds.\n * - An object representing a style change\n * - A StartAnimationFunction that starts eased transition and calls different render\n *      because of course in Recharts we have to have three ways to do everything\n * - An arbitrary function to be executed\n */\n\nexport function createAnimateManager(timeoutController) {\n  var currStyle = {};\n  var handleChange = () => null;\n  var shouldStop = false;\n  var cancelTimeout = null;\n  var setStyle = _style => {\n    if (shouldStop) {\n      return;\n    }\n    if (Array.isArray(_style)) {\n      if (!_style.length) {\n        return;\n      }\n      var styles = _style;\n      var [curr, ...restStyles] = styles;\n      if (typeof curr === 'number') {\n        cancelTimeout = timeoutController.setTimeout(setStyle.bind(null, restStyles), curr);\n        return;\n      }\n      setStyle(curr);\n      cancelTimeout = timeoutController.setTimeout(setStyle.bind(null, restStyles));\n      return;\n    }\n    if (typeof _style === 'object') {\n      currStyle = _style;\n      handleChange(currStyle);\n    }\n    if (typeof _style === 'function') {\n      _style();\n    }\n  };\n  return {\n    stop: () => {\n      shouldStop = true;\n    },\n    start: style => {\n      shouldStop = false;\n      if (cancelTimeout) {\n        cancelTimeout();\n        cancelTimeout = null;\n      }\n      setStyle(style);\n    },\n    subscribe: _handleChange => {\n      handleChange = _handleChange;\n      return () => {\n        handleChange = () => null;\n      };\n    },\n    getTimeoutController: () => timeoutController\n  };\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASA,oBAAoBA,CAACC,iBAAiB,EAAE;EACtD,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,YAAY,GAAGA,CAAA,KAAM,IAAI;EAC7B,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,aAAa,GAAG,IAAI;EACxB,IAAIC,QAAQ,GAAGC,MAAM,IAAI;IACvB,IAAIH,UAAU,EAAE;MACd;IACF;IACA,IAAII,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzB,IAAI,CAACA,MAAM,CAACG,MAAM,EAAE;QAClB;MACF;MACA,IAAIC,MAAM,GAAGJ,MAAM;MACnB,IAAI,CAACK,IAAI,EAAE,GAAGC,UAAU,CAAC,GAAGF,MAAM;MAClC,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;QAC5BP,aAAa,GAAGJ,iBAAiB,CAACa,UAAU,CAACR,QAAQ,CAACS,IAAI,CAAC,IAAI,EAAEF,UAAU,CAAC,EAAED,IAAI,CAAC;QACnF;MACF;MACAN,QAAQ,CAACM,IAAI,CAAC;MACdP,aAAa,GAAGJ,iBAAiB,CAACa,UAAU,CAACR,QAAQ,CAACS,IAAI,CAAC,IAAI,EAAEF,UAAU,CAAC,CAAC;MAC7E;IACF;IACA,IAAI,OAAON,MAAM,KAAK,QAAQ,EAAE;MAC9BL,SAAS,GAAGK,MAAM;MAClBJ,YAAY,CAACD,SAAS,CAAC;IACzB;IACA,IAAI,OAAOK,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EACD,OAAO;IACLS,IAAI,EAAEA,CAAA,KAAM;MACVZ,UAAU,GAAG,IAAI;IACnB,CAAC;IACDa,KAAK,EAAEC,KAAK,IAAI;MACdd,UAAU,GAAG,KAAK;MAClB,IAAIC,aAAa,EAAE;QACjBA,aAAa,CAAC,CAAC;QACfA,aAAa,GAAG,IAAI;MACtB;MACAC,QAAQ,CAACY,KAAK,CAAC;IACjB,CAAC;IACDC,SAAS,EAAEC,aAAa,IAAI;MAC1BjB,YAAY,GAAGiB,aAAa;MAC5B,OAAO,MAAM;QACXjB,YAAY,GAAGA,CAAA,KAAM,IAAI;MAC3B,CAAC;IACH,CAAC;IACDkB,oBAAoB,EAAEA,CAAA,KAAMpB;EAC9B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}