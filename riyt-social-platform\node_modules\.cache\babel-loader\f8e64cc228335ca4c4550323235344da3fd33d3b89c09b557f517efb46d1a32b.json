{"ast": null, "code": "import { daysInYear } from \"./constants.js\";\n\n/**\n * @name milliseconds\n * @category Millisecond Helpers\n * @summary\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * @description\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occurs every 4 years, except for years that are divisible by 100 and not divisible by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n *\n * One month is a year divided by 12.\n *\n * @param duration - The object with years, months, weeks, days, hours, minutes and seconds to be added.\n *\n * @returns The milliseconds\n *\n * @example\n * // 1 year in milliseconds\n * milliseconds({ years: 1 })\n * //=> 31556952000\n *\n * // 3 months in milliseconds\n * milliseconds({ months: 3 })\n * //=> 7889238000\n */\nexport function milliseconds({\n  years,\n  months,\n  weeks,\n  days,\n  hours,\n  minutes,\n  seconds\n}) {\n  let totalDays = 0;\n  if (years) totalDays += years * daysInYear;\n  if (months) totalDays += months * (daysInYear / 12);\n  if (weeks) totalDays += weeks * 7;\n  if (days) totalDays += days;\n  let totalSeconds = totalDays * 24 * 60 * 60;\n  if (hours) totalSeconds += hours * 60 * 60;\n  if (minutes) totalSeconds += minutes * 60;\n  if (seconds) totalSeconds += seconds;\n  return Math.trunc(totalSeconds * 1000);\n}\n\n// Fallback for modularized imports:\nexport default milliseconds;", "map": {"version": 3, "names": ["daysInYear", "milliseconds", "years", "months", "weeks", "days", "hours", "minutes", "seconds", "totalDays", "totalSeconds", "Math", "trunc"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/milliseconds.js"], "sourcesContent": ["import { daysInYear } from \"./constants.js\";\n\n/**\n * @name milliseconds\n * @category Millisecond Helpers\n * @summary\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * @description\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occurs every 4 years, except for years that are divisible by 100 and not divisible by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n *\n * One month is a year divided by 12.\n *\n * @param duration - The object with years, months, weeks, days, hours, minutes and seconds to be added.\n *\n * @returns The milliseconds\n *\n * @example\n * // 1 year in milliseconds\n * milliseconds({ years: 1 })\n * //=> 31556952000\n *\n * // 3 months in milliseconds\n * milliseconds({ months: 3 })\n * //=> 7889238000\n */\nexport function milliseconds({\n  years,\n  months,\n  weeks,\n  days,\n  hours,\n  minutes,\n  seconds,\n}) {\n  let totalDays = 0;\n\n  if (years) totalDays += years * daysInYear;\n  if (months) totalDays += months * (daysInYear / 12);\n  if (weeks) totalDays += weeks * 7;\n  if (days) totalDays += days;\n\n  let totalSeconds = totalDays * 24 * 60 * 60;\n\n  if (hours) totalSeconds += hours * 60 * 60;\n  if (minutes) totalSeconds += minutes * 60;\n  if (seconds) totalSeconds += seconds;\n\n  return Math.trunc(totalSeconds * 1000);\n}\n\n// Fallback for modularized imports:\nexport default milliseconds;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,gBAAgB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAAC;EAC3BC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,IAAI;EACJC,KAAK;EACLC,OAAO;EACPC;AACF,CAAC,EAAE;EACD,IAAIC,SAAS,GAAG,CAAC;EAEjB,IAAIP,KAAK,EAAEO,SAAS,IAAIP,KAAK,GAAGF,UAAU;EAC1C,IAAIG,MAAM,EAAEM,SAAS,IAAIN,MAAM,IAAIH,UAAU,GAAG,EAAE,CAAC;EACnD,IAAII,KAAK,EAAEK,SAAS,IAAIL,KAAK,GAAG,CAAC;EACjC,IAAIC,IAAI,EAAEI,SAAS,IAAIJ,IAAI;EAE3B,IAAIK,YAAY,GAAGD,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAE3C,IAAIH,KAAK,EAAEI,YAAY,IAAIJ,KAAK,GAAG,EAAE,GAAG,EAAE;EAC1C,IAAIC,OAAO,EAAEG,YAAY,IAAIH,OAAO,GAAG,EAAE;EACzC,IAAIC,OAAO,EAAEE,YAAY,IAAIF,OAAO;EAEpC,OAAOG,IAAI,CAACC,KAAK,CAACF,YAAY,GAAG,IAAI,CAAC;AACxC;;AAEA;AACA,eAAeT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}