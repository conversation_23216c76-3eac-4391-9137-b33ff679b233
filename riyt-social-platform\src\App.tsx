import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import DashboardLayout from './components/dashboard/DashboardLayout';
import DashboardPage from './pages/DashboardPage';
import SocialAccountsPage from './pages/SocialAccountsPage';
import CreatePostPageEnhanced from './pages/CreatePostPageEnhanced';
import ScheduledPostsPage from './pages/ScheduledPostsPage';
import MediaLibraryPage from './pages/MediaLibraryPage';
import ContentCalendarPage from './pages/ContentCalendarPage';
import AnalyticsPage from './pages/AnalyticsPage';
import TeamPage from './pages/TeamPage';
import AutomationPage from './pages/AutomationPage';
import OAuthCallbackPage from './pages/OAuthCallbackPage';
import { Box, Typography, Button, Container } from '@mui/material';
import { Brightness4, Brightness7 } from '@mui/icons-material';
import { useTheme } from './contexts/ThemeContext';



// Demo auth context
const DemoAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>;
};

// Temporary welcome page to test theme
const WelcomePage: React.FC = () => {
  const { mode, toggleTheme } = useTheme();

  return (
    <Container maxWidth="lg">
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        gap={4}
      >
        <Typography variant="h2" component="h1" textAlign="center">
          Welcome to Riyt Social Platform
        </Typography>
        <Typography variant="h6" color="text.secondary" textAlign="center">
          Your all-in-one social media management solution
        </Typography>

        <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
          <Button
            variant="outlined"
            onClick={toggleTheme}
            startIcon={mode === 'light' ? <Brightness4 /> : <Brightness7 />}
          >
            Switch to {mode === 'light' ? 'Dark' : 'Light'} Mode
          </Button>

          <Button
            variant="contained"
            href="/demo"
            size="large"
          >
            View Demo Dashboard
          </Button>
        </Box>

        <Typography variant="body2" color="text.secondary" textAlign="center">
          🚀 UI Framework working! Firebase setup needed for full functionality.
          <br />
          Click "View Demo Dashboard" to see the complete interface.
        </Typography>
      </Box>
    </Container>
  );
};

// Temporary dashboard with demo user
const DemoDashboardPage: React.FC = () => {
  return <DashboardPage />;
};

// App routes component
const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<WelcomePage />} />
      <Route path="/demo" element={<DashboardLayout />}>
        <Route index element={<DemoDashboardPage />} />
        <Route path="create" element={<CreatePostPageEnhanced />} />
        <Route path="scheduled" element={<ScheduledPostsPage />} />
        <Route path="calendar" element={<ContentCalendarPage />} />
        <Route path="media" element={<MediaLibraryPage />} />
        <Route path="analytics" element={<AnalyticsPage />} />
        <Route path="accounts" element={<SocialAccountsPage />} />
        <Route path="team" element={<TeamPage />} />
        <Route path="automation" element={<AutomationPage />} />
        <Route path="settings" element={<div>Settings Page (Coming Soon)</div>} />
        <Route path="help" element={<div>Help & Support Page (Coming Soon)</div>} />
      </Route>

      {/* OAuth callback routes */}
      <Route path="/auth/:platform/callback" element={<OAuthCallbackPage />} />
    </Routes>
  );
};

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <DemoAuthProvider>
          <Router>
            <AppRoutes />
          </Router>
        </DemoAuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
