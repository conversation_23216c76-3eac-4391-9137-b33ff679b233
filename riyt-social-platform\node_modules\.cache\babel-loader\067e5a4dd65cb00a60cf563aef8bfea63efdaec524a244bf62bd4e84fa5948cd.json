{"ast": null, "code": "import exponent from \"./exponent.js\";\nimport formatGroup from \"./formatGroup.js\";\nimport formatNumerals from \"./formatNumerals.js\";\nimport formatSpecifier from \"./formatSpecifier.js\";\nimport formatTrim from \"./formatTrim.js\";\nimport formatTypes from \"./formatTypes.js\";\nimport { prefixExponent } from \"./formatPrefixAuto.js\";\nimport identity from \"./identity.js\";\nvar map = Array.prototype.map,\n  prefixes = [\"y\", \"z\", \"a\", \"f\", \"p\", \"n\", \"µ\", \"m\", \"\", \"k\", \"M\", \"G\", \"T\", \"P\", \"E\", \"Z\", \"Y\"];\nexport default function (locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + \"\"),\n    currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n    currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n    decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n    numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),\n    percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n    minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n    nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n  function newFormat(specifier) {\n    specifier = formatSpecifier(specifier);\n    var fill = specifier.fill,\n      align = specifier.align,\n      sign = specifier.sign,\n      symbol = specifier.symbol,\n      zero = specifier.zero,\n      width = specifier.width,\n      comma = specifier.comma,\n      precision = specifier.precision,\n      trim = specifier.trim,\n      type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || fill === \"0\" && align === \"=\") zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n      suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = formatTypes[type],\n      maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));\n    function format(value) {\n      var valuePrefix = prefix,\n        valueSuffix = suffix,\n        i,\n        n,\n        c;\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = formatTrim(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? sign === \"(\" ? sign : minus : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n        padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\":\n          value = valuePrefix + value + valueSuffix + padding;\n          break;\n        case \"=\":\n          value = valuePrefix + padding + value + valueSuffix;\n          break;\n        case \"^\":\n          value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);\n          break;\n        default:\n          value = padding + valuePrefix + value + valueSuffix;\n          break;\n      }\n      return numerals(value);\n    }\n    format.toString = function () {\n      return specifier + \"\";\n    };\n    return format;\n  }\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = \"f\", specifier)),\n      e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,\n      k = Math.pow(10, -e),\n      prefix = prefixes[8 + e / 3];\n    return function (value) {\n      return f(k * value) + prefix;\n    };\n  }\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}", "map": {"version": 3, "names": ["exponent", "formatGroup", "formatNumerals", "formatSpecifier", "formatTrim", "formatTypes", "prefixExponent", "identity", "map", "Array", "prototype", "prefixes", "locale", "group", "grouping", "undefined", "thousands", "call", "Number", "currencyPrefix", "currency", "currencySuffix", "decimal", "numerals", "String", "percent", "minus", "nan", "newFormat", "specifier", "fill", "align", "sign", "symbol", "zero", "width", "comma", "precision", "trim", "type", "prefix", "test", "toLowerCase", "suffix", "formatType", "maybeSuffix", "Math", "max", "min", "format", "value", "valuePrefix", "valueSuffix", "i", "n", "c", "valueNegative", "isNaN", "abs", "length", "charCodeAt", "slice", "Infinity", "padding", "join", "toString", "formatPrefix", "f", "e", "floor", "k", "pow"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-format/src/locale.js"], "sourcesContent": ["import exponent from \"./exponent.js\";\nimport formatGroup from \"./formatGroup.js\";\nimport formatNumerals from \"./formatNumerals.js\";\nimport formatSpecifier from \"./formatSpecifier.js\";\nimport formatTrim from \"./formatTrim.js\";\nimport formatTypes from \"./formatTypes.js\";\nimport {prefixExponent} from \"./formatPrefixAuto.js\";\nimport identity from \"./identity.js\";\n\nvar map = Array.prototype.map,\n    prefixes = [\"y\",\"z\",\"a\",\"f\",\"p\",\"n\",\"µ\",\"m\",\"\",\"k\",\"M\",\"G\",\"T\",\"P\",\"E\",\"Z\",\"Y\"];\n\nexport default function(locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + \"\"),\n      currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n      currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n      decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n      numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),\n      percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n      minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n      nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n\n  function newFormat(specifier) {\n    specifier = formatSpecifier(specifier);\n\n    var fill = specifier.fill,\n        align = specifier.align,\n        sign = specifier.sign,\n        symbol = specifier.symbol,\n        zero = specifier.zero,\n        width = specifier.width,\n        comma = specifier.comma,\n        precision = specifier.precision,\n        trim = specifier.trim,\n        type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || (fill === \"0\" && align === \"=\")) zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n        suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = formatTypes[type],\n        maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6\n        : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))\n        : Math.max(0, Math.min(20, precision));\n\n    function format(value) {\n      var valuePrefix = prefix,\n          valueSuffix = suffix,\n          i, n, c;\n\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = formatTrim(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? (sign === \"(\" ? sign : minus) : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n          padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\": value = valuePrefix + value + valueSuffix + padding; break;\n        case \"=\": value = valuePrefix + padding + value + valueSuffix; break;\n        case \"^\": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;\n        default: value = padding + valuePrefix + value + valueSuffix; break;\n      }\n\n      return numerals(value);\n    }\n\n    format.toString = function() {\n      return specifier + \"\";\n    };\n\n    return format;\n  }\n\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = \"f\", specifier)),\n        e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,\n        k = Math.pow(10, -e),\n        prefix = prefixes[8 + e / 3];\n    return function(value) {\n      return f(k * value) + prefix;\n    };\n  }\n\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAAQC,cAAc,QAAO,uBAAuB;AACpD,OAAOC,QAAQ,MAAM,eAAe;AAEpC,IAAIC,GAAG,GAAGC,KAAK,CAACC,SAAS,CAACF,GAAG;EACzBG,QAAQ,GAAG,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC;AAEnF,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,KAAK,GAAGD,MAAM,CAACE,QAAQ,KAAKC,SAAS,IAAIH,MAAM,CAACI,SAAS,KAAKD,SAAS,GAAGR,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACS,IAAI,CAACL,MAAM,CAACE,QAAQ,EAAEI,MAAM,CAAC,EAAEN,MAAM,CAACI,SAAS,GAAG,EAAE,CAAC;IAC1JG,cAAc,GAAGP,MAAM,CAACQ,QAAQ,KAAKL,SAAS,GAAG,EAAE,GAAGH,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE;IAC7EC,cAAc,GAAGT,MAAM,CAACQ,QAAQ,KAAKL,SAAS,GAAG,EAAE,GAAGH,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE;IAC7EE,OAAO,GAAGV,MAAM,CAACU,OAAO,KAAKP,SAAS,GAAG,GAAG,GAAGH,MAAM,CAACU,OAAO,GAAG,EAAE;IAClEC,QAAQ,GAAGX,MAAM,CAACW,QAAQ,KAAKR,SAAS,GAAGR,QAAQ,GAAGL,cAAc,CAACM,GAAG,CAACS,IAAI,CAACL,MAAM,CAACW,QAAQ,EAAEC,MAAM,CAAC,CAAC;IACvGC,OAAO,GAAGb,MAAM,CAACa,OAAO,KAAKV,SAAS,GAAG,GAAG,GAAGH,MAAM,CAACa,OAAO,GAAG,EAAE;IAClEC,KAAK,GAAGd,MAAM,CAACc,KAAK,KAAKX,SAAS,GAAG,GAAG,GAAGH,MAAM,CAACc,KAAK,GAAG,EAAE;IAC5DC,GAAG,GAAGf,MAAM,CAACe,GAAG,KAAKZ,SAAS,GAAG,KAAK,GAAGH,MAAM,CAACe,GAAG,GAAG,EAAE;EAE5D,SAASC,SAASA,CAACC,SAAS,EAAE;IAC5BA,SAAS,GAAG1B,eAAe,CAAC0B,SAAS,CAAC;IAEtC,IAAIC,IAAI,GAAGD,SAAS,CAACC,IAAI;MACrBC,KAAK,GAAGF,SAAS,CAACE,KAAK;MACvBC,IAAI,GAAGH,SAAS,CAACG,IAAI;MACrBC,MAAM,GAAGJ,SAAS,CAACI,MAAM;MACzBC,IAAI,GAAGL,SAAS,CAACK,IAAI;MACrBC,KAAK,GAAGN,SAAS,CAACM,KAAK;MACvBC,KAAK,GAAGP,SAAS,CAACO,KAAK;MACvBC,SAAS,GAAGR,SAAS,CAACQ,SAAS;MAC/BC,IAAI,GAAGT,SAAS,CAACS,IAAI;MACrBC,IAAI,GAAGV,SAAS,CAACU,IAAI;;IAEzB;IACA,IAAIA,IAAI,KAAK,GAAG,EAAEH,KAAK,GAAG,IAAI,EAAEG,IAAI,GAAG,GAAG;;IAE1C;IAAA,KACK,IAAI,CAAClC,WAAW,CAACkC,IAAI,CAAC,EAAEF,SAAS,KAAKtB,SAAS,KAAKsB,SAAS,GAAG,EAAE,CAAC,EAAEC,IAAI,GAAG,IAAI,EAAEC,IAAI,GAAG,GAAG;;IAEjG;IACA,IAAIL,IAAI,IAAKJ,IAAI,KAAK,GAAG,IAAIC,KAAK,KAAK,GAAI,EAAEG,IAAI,GAAG,IAAI,EAAEJ,IAAI,GAAG,GAAG,EAAEC,KAAK,GAAG,GAAG;;IAEjF;IACA;IACA,IAAIS,MAAM,GAAGP,MAAM,KAAK,GAAG,GAAGd,cAAc,GAAGc,MAAM,KAAK,GAAG,IAAI,QAAQ,CAACQ,IAAI,CAACF,IAAI,CAAC,GAAG,GAAG,GAAGA,IAAI,CAACG,WAAW,CAAC,CAAC,GAAG,EAAE;MAChHC,MAAM,GAAGV,MAAM,KAAK,GAAG,GAAGZ,cAAc,GAAG,MAAM,CAACoB,IAAI,CAACF,IAAI,CAAC,GAAGd,OAAO,GAAG,EAAE;;IAE/E;IACA;IACA;IACA,IAAImB,UAAU,GAAGvC,WAAW,CAACkC,IAAI,CAAC;MAC9BM,WAAW,GAAG,YAAY,CAACJ,IAAI,CAACF,IAAI,CAAC;;IAEzC;IACA;IACA;IACA;IACAF,SAAS,GAAGA,SAAS,KAAKtB,SAAS,GAAG,CAAC,GACjC,QAAQ,CAAC0B,IAAI,CAACF,IAAI,CAAC,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEX,SAAS,CAAC,CAAC,GAC1DS,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEX,SAAS,CAAC,CAAC;IAE1C,SAASY,MAAMA,CAACC,KAAK,EAAE;MACrB,IAAIC,WAAW,GAAGX,MAAM;QACpBY,WAAW,GAAGT,MAAM;QACpBU,CAAC;QAAEC,CAAC;QAAEC,CAAC;MAEX,IAAIhB,IAAI,KAAK,GAAG,EAAE;QAChBa,WAAW,GAAGR,UAAU,CAACM,KAAK,CAAC,GAAGE,WAAW;QAC7CF,KAAK,GAAG,EAAE;MACZ,CAAC,MAAM;QACLA,KAAK,GAAG,CAACA,KAAK;;QAEd;QACA,IAAIM,aAAa,GAAGN,KAAK,GAAG,CAAC,IAAI,CAAC,GAAGA,KAAK,GAAG,CAAC;;QAE9C;QACAA,KAAK,GAAGO,KAAK,CAACP,KAAK,CAAC,GAAGvB,GAAG,GAAGiB,UAAU,CAACE,IAAI,CAACY,GAAG,CAACR,KAAK,CAAC,EAAEb,SAAS,CAAC;;QAEnE;QACA,IAAIC,IAAI,EAAEY,KAAK,GAAG9C,UAAU,CAAC8C,KAAK,CAAC;;QAEnC;QACA,IAAIM,aAAa,IAAI,CAACN,KAAK,KAAK,CAAC,IAAIlB,IAAI,KAAK,GAAG,EAAEwB,aAAa,GAAG,KAAK;;QAExE;QACAL,WAAW,GAAG,CAACK,aAAa,GAAIxB,IAAI,KAAK,GAAG,GAAGA,IAAI,GAAGN,KAAK,GAAIM,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,GAAG,EAAE,GAAGA,IAAI,IAAImB,WAAW;QACtHC,WAAW,GAAG,CAACb,IAAI,KAAK,GAAG,GAAG5B,QAAQ,CAAC,CAAC,GAAGL,cAAc,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI8C,WAAW,IAAII,aAAa,IAAIxB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;;QAE/H;QACA;QACA,IAAIa,WAAW,EAAE;UACfQ,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGJ,KAAK,CAACS,MAAM;UACxB,OAAO,EAAEN,CAAC,GAAGC,CAAC,EAAE;YACd,IAAIC,CAAC,GAAGL,KAAK,CAACU,UAAU,CAACP,CAAC,CAAC,EAAE,EAAE,GAAGE,CAAC,IAAIA,CAAC,GAAG,EAAE,EAAE;cAC7CH,WAAW,GAAG,CAACG,CAAC,KAAK,EAAE,GAAGjC,OAAO,GAAG4B,KAAK,CAACW,KAAK,CAACR,CAAC,GAAG,CAAC,CAAC,GAAGH,KAAK,CAACW,KAAK,CAACR,CAAC,CAAC,IAAID,WAAW;cACtFF,KAAK,GAAGA,KAAK,CAACW,KAAK,CAAC,CAAC,EAAER,CAAC,CAAC;cACzB;YACF;UACF;QACF;MACF;;MAEA;MACA,IAAIjB,KAAK,IAAI,CAACF,IAAI,EAAEgB,KAAK,GAAGrC,KAAK,CAACqC,KAAK,EAAEY,QAAQ,CAAC;;MAElD;MACA,IAAIH,MAAM,GAAGR,WAAW,CAACQ,MAAM,GAAGT,KAAK,CAACS,MAAM,GAAGP,WAAW,CAACO,MAAM;QAC/DI,OAAO,GAAGJ,MAAM,GAAGxB,KAAK,GAAG,IAAI1B,KAAK,CAAC0B,KAAK,GAAGwB,MAAM,GAAG,CAAC,CAAC,CAACK,IAAI,CAAClC,IAAI,CAAC,GAAG,EAAE;;MAE5E;MACA,IAAIM,KAAK,IAAIF,IAAI,EAAEgB,KAAK,GAAGrC,KAAK,CAACkD,OAAO,GAAGb,KAAK,EAAEa,OAAO,CAACJ,MAAM,GAAGxB,KAAK,GAAGiB,WAAW,CAACO,MAAM,GAAGG,QAAQ,CAAC,EAAEC,OAAO,GAAG,EAAE;;MAEvH;MACA,QAAQhC,KAAK;QACX,KAAK,GAAG;UAAEmB,KAAK,GAAGC,WAAW,GAAGD,KAAK,GAAGE,WAAW,GAAGW,OAAO;UAAE;QAC/D,KAAK,GAAG;UAAEb,KAAK,GAAGC,WAAW,GAAGY,OAAO,GAAGb,KAAK,GAAGE,WAAW;UAAE;QAC/D,KAAK,GAAG;UAAEF,KAAK,GAAGa,OAAO,CAACF,KAAK,CAAC,CAAC,EAAEF,MAAM,GAAGI,OAAO,CAACJ,MAAM,IAAI,CAAC,CAAC,GAAGR,WAAW,GAAGD,KAAK,GAAGE,WAAW,GAAGW,OAAO,CAACF,KAAK,CAACF,MAAM,CAAC;UAAE;QAC9H;UAAST,KAAK,GAAGa,OAAO,GAAGZ,WAAW,GAAGD,KAAK,GAAGE,WAAW;UAAE;MAChE;MAEA,OAAO7B,QAAQ,CAAC2B,KAAK,CAAC;IACxB;IAEAD,MAAM,CAACgB,QAAQ,GAAG,YAAW;MAC3B,OAAOpC,SAAS,GAAG,EAAE;IACvB,CAAC;IAED,OAAOoB,MAAM;EACf;EAEA,SAASiB,YAAYA,CAACrC,SAAS,EAAEqB,KAAK,EAAE;IACtC,IAAIiB,CAAC,GAAGvC,SAAS,EAAEC,SAAS,GAAG1B,eAAe,CAAC0B,SAAS,CAAC,EAAEA,SAAS,CAACU,IAAI,GAAG,GAAG,EAAEV,SAAS,CAAC,CAAC;MACxFuC,CAAC,GAAGtB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACuB,KAAK,CAACrE,QAAQ,CAACkD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MAClEoB,CAAC,GAAGxB,IAAI,CAACyB,GAAG,CAAC,EAAE,EAAE,CAACH,CAAC,CAAC;MACpB5B,MAAM,GAAG7B,QAAQ,CAAC,CAAC,GAAGyD,CAAC,GAAG,CAAC,CAAC;IAChC,OAAO,UAASlB,KAAK,EAAE;MACrB,OAAOiB,CAAC,CAACG,CAAC,GAAGpB,KAAK,CAAC,GAAGV,MAAM;IAC9B,CAAC;EACH;EAEA,OAAO;IACLS,MAAM,EAAErB,SAAS;IACjBsC,YAAY,EAAEA;EAChB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}