import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  ExpandMore as ExpandMoreIcon,
  AutoAwesome as AutoIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingIcon,
  Reply as ReplyIcon,
  Share as ShareIcon,
  Notifications as NotificationIcon,
} from '@mui/icons-material';

interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: {
    type: 'schedule' | 'engagement' | 'mention' | 'hashtag' | 'keyword';
    condition: string;
    value: string;
  };
  action: {
    type: 'post' | 'reply' | 'repost' | 'notify' | 'analyze';
    content?: string;
    platforms: string[];
  };
  isActive: boolean;
  createdAt: Date;
  lastTriggered?: Date;
  triggerCount: number;
}

interface ContentTemplate {
  id: string;
  name: string;
  category: 'promotional' | 'educational' | 'engagement' | 'seasonal';
  content: string;
  platforms: string[];
  variables: string[];
  usageCount: number;
}

const AutomationPage: React.FC = () => {
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([]);
  const [contentTemplates, setContentTemplates] = useState<ContentTemplate[]>([]);
  const [ruleDialogOpen, setRuleDialogOpen] = useState(false);
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [newRule, setNewRule] = useState<Partial<AutomationRule>>({});
  const [newTemplate, setNewTemplate] = useState<Partial<ContentTemplate>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Mock data for demo
  useEffect(() => {
    const mockRules: AutomationRule[] = [
      {
        id: 'rule_1',
        name: 'Daily Motivation Posts',
        description: 'Automatically post motivational content every Monday at 9 AM',
        trigger: {
          type: 'schedule',
          condition: 'weekly',
          value: 'monday_09:00',
        },
        action: {
          type: 'post',
          content: 'Monday Motivation: {{motivational_quote}} #MondayMotivation #Success',
          platforms: ['facebook', 'linkedin'],
        },
        isActive: true,
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        lastTriggered: new Date(Date.now() - 24 * 60 * 60 * 1000),
        triggerCount: 12,
      },
      {
        id: 'rule_2',
        name: 'Auto-Reply to Mentions',
        description: 'Automatically reply to brand mentions with a thank you message',
        trigger: {
          type: 'mention',
          condition: 'contains',
          value: '@riyt',
        },
        action: {
          type: 'reply',
          content: 'Thank you for mentioning us! We appreciate your support! 🙏',
          platforms: ['twitter'],
        },
        isActive: true,
        createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
        lastTriggered: new Date(Date.now() - 2 * 60 * 60 * 1000),
        triggerCount: 45,
      },
      {
        id: 'rule_3',
        name: 'Trending Hashtag Monitor',
        description: 'Monitor trending hashtags and notify when relevant ones appear',
        trigger: {
          type: 'hashtag',
          condition: 'trending',
          value: '#socialmedia,#marketing,#automation',
        },
        action: {
          type: 'notify',
          platforms: ['all'],
        },
        isActive: false,
        createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
        triggerCount: 8,
      },
    ];

    const mockTemplates: ContentTemplate[] = [
      {
        id: 'template_1',
        name: 'Product Feature Announcement',
        category: 'promotional',
        content: '🚀 Exciting news! We\'re thrilled to announce {{feature_name}}! {{description}} Learn more: {{link}} #ProductUpdate #Innovation',
        platforms: ['facebook', 'linkedin', 'twitter'],
        variables: ['feature_name', 'description', 'link'],
        usageCount: 15,
      },
      {
        id: 'template_2',
        name: 'Weekly Tips',
        category: 'educational',
        content: '💡 {{day}} Tip: {{tip_content}} What\'s your favorite {{topic}} tip? Share in the comments! #Tips #{{topic}}',
        platforms: ['facebook', 'linkedin'],
        variables: ['day', 'tip_content', 'topic'],
        usageCount: 28,
      },
      {
        id: 'template_3',
        name: 'Engagement Question',
        category: 'engagement',
        content: '🤔 Quick question for our community: {{question}} Let us know your thoughts in the comments! #Community #Discussion',
        platforms: ['facebook', 'instagram'],
        variables: ['question'],
        usageCount: 22,
      },
      {
        id: 'template_4',
        name: 'Holiday Greetings',
        category: 'seasonal',
        content: '🎉 Happy {{holiday}}! {{message}} Wishing you and your loved ones {{wishes}}! #{{holiday}} #Celebration',
        platforms: ['facebook', 'instagram', 'twitter'],
        variables: ['holiday', 'message', 'wishes'],
        usageCount: 8,
      },
    ];

    setAutomationRules(mockRules);
    setContentTemplates(mockTemplates);
  }, []);

  const handleToggleRule = async (ruleId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setAutomationRules(prev => 
        prev.map(rule => 
          rule.id === ruleId 
            ? { ...rule, isActive: !rule.isActive }
            : rule
        )
      );
      
      setSuccess('Automation rule updated successfully');
    } catch (err: any) {
      setError(`Failed to update rule: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRule = async () => {
    if (!newRule.name || !newRule.trigger || !newRule.action) return;

    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const rule: AutomationRule = {
        id: `rule_${Date.now()}`,
        name: newRule.name,
        description: newRule.description || '',
        trigger: newRule.trigger,
        action: newRule.action,
        isActive: true,
        createdAt: new Date(),
        triggerCount: 0,
      } as AutomationRule;

      setAutomationRules(prev => [...prev, rule]);
      setSuccess('Automation rule created successfully');
      setRuleDialogOpen(false);
      setNewRule({});
    } catch (err: any) {
      setError(`Failed to create rule: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = async () => {
    if (!newTemplate.name || !newTemplate.content) return;

    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const template: ContentTemplate = {
        id: `template_${Date.now()}`,
        name: newTemplate.name,
        category: newTemplate.category || 'promotional',
        content: newTemplate.content,
        platforms: newTemplate.platforms || [],
        variables: extractVariables(newTemplate.content),
        usageCount: 0,
      } as ContentTemplate;

      setContentTemplates(prev => [...prev, template]);
      setSuccess('Content template created successfully');
      setTemplateDialogOpen(false);
      setNewTemplate({});
    } catch (err: any) {
      setError(`Failed to create template: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const extractVariables = (content: string): string[] => {
    const matches = content.match(/\{\{([^}]+)\}\}/g);
    return matches ? matches.map(match => match.slice(2, -2)) : [];
  };

  const getTriggerTypeColor = (type: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (type) {
      case 'schedule':
        return 'primary';
      case 'engagement':
        return 'success';
      case 'mention':
        return 'info';
      case 'hashtag':
        return 'secondary';
      case 'keyword':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (category) {
      case 'promotional':
        return 'primary';
      case 'educational':
        return 'info';
      case 'engagement':
        return 'success';
      case 'seasonal':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const formatLastTriggered = (date?: Date): string => {
    if (!date) return 'Never';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Automation & Workflows
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Automate your social media management with smart rules and content templates.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Automation Overview */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Automation Overview
              </Typography>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Active Rules
                </Typography>
                <Typography variant="h6">
                  {automationRules.filter(r => r.isActive).length}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Total Triggers
                </Typography>
                <Typography variant="h6">
                  {automationRules.reduce((sum, r) => sum + r.triggerCount, 0)}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" mb={3}>
                <Typography variant="body2" color="text.secondary">
                  Templates
                </Typography>
                <Typography variant="h6">
                  {contentTemplates.length}
                </Typography>
              </Box>
              <Button
                variant="contained"
                fullWidth
                startIcon={<AddIcon />}
                onClick={() => setRuleDialogOpen(true)}
                sx={{ mb: 1 }}
              >
                Create Rule
              </Button>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<AddIcon />}
                onClick={() => setTemplateDialogOpen(true)}
              >
                Create Template
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Automation Rules */}
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Automation Rules
              </Typography>
              
              {automationRules.length === 0 ? (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={4}>
                  No automation rules created yet
                </Typography>
              ) : (
                <List>
                  {automationRules.map((rule) => (
                    <ListItem key={rule.id} divider>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="subtitle1">{rule.name}</Typography>
                            <Chip
                              label={rule.trigger.type}
                              size="small"
                              color={getTriggerTypeColor(rule.trigger.type)}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {rule.description}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Triggered {rule.triggerCount} times • Last: {formatLastTriggered(rule.lastTriggered)}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box display="flex" alignItems="center" gap={1}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={rule.isActive}
                                onChange={() => handleToggleRule(rule.id)}
                                disabled={loading}
                              />
                            }
                            label=""
                          />
                          <IconButton size="small">
                            <EditIcon />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Content Templates */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Content Templates
              </Typography>
              
              <Grid container spacing={2}>
                {contentTemplates.map((template) => (
                  <Grid size={{ xs: 12, md: 6 }} key={template.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="start" mb={2}>
                          <Typography variant="subtitle1">{template.name}</Typography>
                          <Chip
                            label={template.category}
                            size="small"
                            color={getCategoryColor(template.category)}
                          />
                        </Box>
                        
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {template.content.substring(0, 100)}...
                        </Typography>
                        
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="caption" color="text.secondary">
                            Used {template.usageCount} times
                          </Typography>
                          <Box display="flex" gap={1}>
                            <Button size="small" variant="outlined">
                              Use Template
                            </Button>
                            <IconButton size="small">
                              <EditIcon />
                            </IconButton>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Create Rule Dialog */}
      <Dialog
        open={ruleDialogOpen}
        onClose={() => setRuleDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create Automation Rule</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Rule Name"
            value={newRule.name || ''}
            onChange={(e) => setNewRule(prev => ({ ...prev, name: e.target.value }))}
            sx={{ mb: 2, mt: 1 }}
          />
          <TextField
            fullWidth
            label="Description"
            multiline
            rows={2}
            value={newRule.description || ''}
            onChange={(e) => setNewRule(prev => ({ ...prev, description: e.target.value }))}
            sx={{ mb: 2 }}
          />
          
          <Typography variant="subtitle2" gutterBottom>
            Trigger Configuration
          </Typography>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Trigger Type</InputLabel>
            <Select
              value={newRule.trigger?.type || ''}
              onChange={(e) => setNewRule(prev => ({ 
                ...prev, 
                trigger: { ...prev.trigger, type: e.target.value } as any 
              }))}
              label="Trigger Type"
            >
              <MenuItem value="schedule">Schedule</MenuItem>
              <MenuItem value="engagement">Engagement</MenuItem>
              <MenuItem value="mention">Mention</MenuItem>
              <MenuItem value="hashtag">Hashtag</MenuItem>
              <MenuItem value="keyword">Keyword</MenuItem>
            </Select>
          </FormControl>
          
          <Typography variant="subtitle2" gutterBottom>
            Action Configuration
          </Typography>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Action Type</InputLabel>
            <Select
              value={newRule.action?.type || ''}
              onChange={(e) => setNewRule(prev => ({ 
                ...prev, 
                action: { ...prev.action, type: e.target.value } as any 
              }))}
              label="Action Type"
            >
              <MenuItem value="post">Create Post</MenuItem>
              <MenuItem value="reply">Auto Reply</MenuItem>
              <MenuItem value="repost">Repost Content</MenuItem>
              <MenuItem value="notify">Send Notification</MenuItem>
              <MenuItem value="analyze">Analyze Content</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRuleDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateRule}
            variant="contained"
            disabled={loading || !newRule.name}
          >
            {loading ? 'Creating...' : 'Create Rule'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Template Dialog */}
      <Dialog
        open={templateDialogOpen}
        onClose={() => setTemplateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create Content Template</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Template Name"
            value={newTemplate.name || ''}
            onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
            sx={{ mb: 2, mt: 1 }}
          />
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={newTemplate.category || 'promotional'}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, category: e.target.value as any }))}
              label="Category"
            >
              <MenuItem value="promotional">Promotional</MenuItem>
              <MenuItem value="educational">Educational</MenuItem>
              <MenuItem value="engagement">Engagement</MenuItem>
              <MenuItem value="seasonal">Seasonal</MenuItem>
            </Select>
          </FormControl>
          
          <TextField
            fullWidth
            label="Content Template"
            multiline
            rows={4}
            value={newTemplate.content || ''}
            onChange={(e) => setNewTemplate(prev => ({ ...prev, content: e.target.value }))}
            helperText="Use {{variable_name}} for dynamic content"
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTemplateDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateTemplate}
            variant="contained"
            disabled={loading || !newTemplate.name || !newTemplate.content}
          >
            {loading ? 'Creating...' : 'Create Template'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AutomationPage;
