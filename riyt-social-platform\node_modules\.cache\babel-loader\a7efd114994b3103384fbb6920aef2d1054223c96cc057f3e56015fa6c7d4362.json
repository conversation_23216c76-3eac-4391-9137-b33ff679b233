{"ast": null, "code": "module.exports = require('../dist/compat/math/range.js').range;", "map": {"version": 3, "names": ["module", "exports", "require", "range"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/es-toolkit/compat/range.js"], "sourcesContent": ["module.exports = require('../dist/compat/math/range.js').range;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,8BAA8B,CAAC,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}