'use strict';
require('../modules/es.error.to-string');
require('../modules/es.array.iterator');
require('../modules/es.object.keys');
require('../modules/es.object.to-string');
require('../modules/es.map');
require('../modules/es.set');
require('../modules/web.dom-exception.constructor');
require('../modules/web.dom-exception.stack');
require('../modules/web.dom-exception.to-string-tag');
require('../modules/web.structured-clone');
var path = require('../internals/path');

module.exports = path.structuredClone;
