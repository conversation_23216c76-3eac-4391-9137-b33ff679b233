{"ast": null, "code": "export default function reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}", "map": {"version": 3, "names": ["reverse", "values", "Symbol", "iterator", "TypeError", "Array", "from"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-array/src/reverse.js"], "sourcesContent": ["export default function reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n"], "mappings": "AAAA,eAAe,SAASA,OAAOA,CAACC,MAAM,EAAE;EACtC,IAAI,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChG,OAAOC,KAAK,CAACC,IAAI,CAACL,MAAM,CAAC,CAACD,OAAO,CAAC,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}