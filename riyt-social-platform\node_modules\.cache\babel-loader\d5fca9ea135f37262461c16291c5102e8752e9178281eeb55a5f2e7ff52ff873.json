{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link startOfTomorrow} function options.\n */\n\n/**\n * @name startOfTomorrow\n * @category Day Helpers\n * @summary Return the start of tomorrow.\n * @pure false\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @returns The start of tomorrow\n *\n * @description\n * Return the start of tomorrow.\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfTomorrow()\n * //=> Tue Oct 7 2014 00:00:00\n */\nexport function startOfTomorrow(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n  const date = constructFrom(options?.in, 0);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfTomorrow;", "map": {"version": 3, "names": ["constructFrom", "constructNow", "startOfTomorrow", "options", "now", "in", "year", "getFullYear", "month", "getMonth", "day", "getDate", "date", "setFullYear", "setHours"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/startOfTomorrow.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link startOfTomorrow} function options.\n */\n\n/**\n * @name startOfTomorrow\n * @category Day Helpers\n * @summary Return the start of tomorrow.\n * @pure false\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @returns The start of tomorrow\n *\n * @description\n * Return the start of tomorrow.\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfTomorrow()\n * //=> Tue Oct 7 2014 00:00:00\n */\nexport function startOfTomorrow(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n\n  const date = constructFrom(options?.in, 0);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfTomorrow;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,mBAAmB;;AAEhD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,OAAO,EAAE;EACvC,MAAMC,GAAG,GAAGH,YAAY,CAACE,OAAO,EAAEE,EAAE,CAAC;EACrC,MAAMC,IAAI,GAAGF,GAAG,CAACG,WAAW,CAAC,CAAC;EAC9B,MAAMC,KAAK,GAAGJ,GAAG,CAACK,QAAQ,CAAC,CAAC;EAC5B,MAAMC,GAAG,GAAGN,GAAG,CAACO,OAAO,CAAC,CAAC;EAEzB,MAAMC,IAAI,GAAGZ,aAAa,CAACG,OAAO,EAAEE,EAAE,EAAE,CAAC,CAAC;EAC1CO,IAAI,CAACC,WAAW,CAACP,IAAI,EAAEE,KAAK,EAAEE,GAAG,GAAG,CAAC,CAAC;EACtCE,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB,OAAOF,IAAI;AACb;;AAEA;AACA,eAAeV,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}