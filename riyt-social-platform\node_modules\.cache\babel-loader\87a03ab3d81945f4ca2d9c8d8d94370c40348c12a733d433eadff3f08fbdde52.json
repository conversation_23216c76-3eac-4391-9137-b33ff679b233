{"ast": null, "code": "export var combineAxisRangeWithReverse = (axisSettings, axisRange) => {\n  if (!axisSettings || !axisRange) {\n    return undefined;\n  }\n  if (axisSettings !== null && axisSettings !== void 0 && axisSettings.reversed) {\n    return [axisRange[1], axisRange[0]];\n  }\n  return axisRange;\n};", "map": {"version": 3, "names": ["combineAxisRangeWithReverse", "axisSettings", "axisRange", "undefined", "reversed"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/state/selectors/combiners/combineAxisRangeWithReverse.js"], "sourcesContent": ["export var combineAxisRangeWithReverse = (axisSettings, axisRange) => {\n  if (!axisSettings || !axisRange) {\n    return undefined;\n  }\n  if (axisSettings !== null && axisSettings !== void 0 && axisSettings.reversed) {\n    return [axisRange[1], axisRange[0]];\n  }\n  return axisRange;\n};"], "mappings": "AAAA,OAAO,IAAIA,2BAA2B,GAAGA,CAACC,YAAY,EAAEC,SAAS,KAAK;EACpE,IAAI,CAACD,YAAY,IAAI,CAACC,SAAS,EAAE;IAC/B,OAAOC,SAAS;EAClB;EACA,IAAIF,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACG,QAAQ,EAAE;IAC7E,OAAO,CAACF,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;EACrC;EACA,OAAOA,SAAS;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}