"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M4 6.55c2.6.77 5.28 1.16 8 1.16s5.41-.39 8-1.16v10.91c-2.6-.77-5.28-1.16-8-1.16s-5.41.39-8 1.16zM2 3.77v16.47s.77-.26.88-.3C5.82 18.85 8.91 18.3 12 18.3s6.18.55 9.12 1.64c.11.04.88.3.88.3V3.77s-.77.26-.88.3C18.18 5.15 15.09 5.71 12 5.71s-6.18-.56-9.12-1.64c-.11-.05-.88-.3-.88-.3"
}), 'PanoramaHorizontalSharp');