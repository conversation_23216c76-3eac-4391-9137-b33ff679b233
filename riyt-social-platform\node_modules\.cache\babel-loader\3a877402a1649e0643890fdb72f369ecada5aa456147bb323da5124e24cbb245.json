{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst identity = require('../../function/identity.js');\nconst property = require('../object/property.js');\nconst matches = require('../predicate/matches.js');\nconst matchesProperty = require('../predicate/matchesProperty.js');\nfunction iteratee(value) {\n  if (value == null) {\n    return identity.identity;\n  }\n  switch (typeof value) {\n    case 'function':\n      {\n        return value;\n      }\n    case 'object':\n      {\n        if (Array.isArray(value) && value.length === 2) {\n          return matchesProperty.matchesProperty(value[0], value[1]);\n        }\n        return matches.matches(value);\n      }\n    case 'string':\n    case 'symbol':\n    case 'number':\n      {\n        return property.property(value);\n      }\n  }\n}\nexports.iteratee = iteratee;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "identity", "require", "property", "matches", "matchesProperty", "iteratee", "Array", "isArray", "length"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/es-toolkit/dist/compat/util/iteratee.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst identity = require('../../function/identity.js');\nconst property = require('../object/property.js');\nconst matches = require('../predicate/matches.js');\nconst matchesProperty = require('../predicate/matchesProperty.js');\n\nfunction iteratee(value) {\n    if (value == null) {\n        return identity.identity;\n    }\n    switch (typeof value) {\n        case 'function': {\n            return value;\n        }\n        case 'object': {\n            if (Array.isArray(value) && value.length === 2) {\n                return matchesProperty.matchesProperty(value[0], value[1]);\n            }\n            return matches.matches(value);\n        }\n        case 'string':\n        case 'symbol':\n        case 'number': {\n            return property.property(value);\n        }\n    }\n}\n\nexports.iteratee = iteratee;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AACtD,MAAMC,QAAQ,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AACjD,MAAME,OAAO,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AAClD,MAAMG,eAAe,GAAGH,OAAO,CAAC,iCAAiC,CAAC;AAElE,SAASI,QAAQA,CAACN,KAAK,EAAE;EACrB,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOC,QAAQ,CAACA,QAAQ;EAC5B;EACA,QAAQ,OAAOD,KAAK;IAChB,KAAK,UAAU;MAAE;QACb,OAAOA,KAAK;MAChB;IACA,KAAK,QAAQ;MAAE;QACX,IAAIO,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,IAAIA,KAAK,CAACS,MAAM,KAAK,CAAC,EAAE;UAC5C,OAAOJ,eAAe,CAACA,eAAe,CAACL,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9D;QACA,OAAOI,OAAO,CAACA,OAAO,CAACJ,KAAK,CAAC;MACjC;IACA,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,QAAQ;MAAE;QACX,OAAOG,QAAQ,CAACA,QAAQ,CAACH,KAAK,CAAC;MACnC;EACJ;AACJ;AAEAH,OAAO,CAACS,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}