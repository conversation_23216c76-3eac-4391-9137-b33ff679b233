{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\ryt\\\\riyt-social-platform\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { authService } from '../services/firebase/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [authState, setAuthState] = useState({\n    user: null,\n    loading: true,\n    error: null\n  });\n  useEffect(() => {\n    const unsubscribe = authService.onAuthStateChanged(firebaseUser => {\n      if (firebaseUser) {\n        const user = authService.getCurrentUser();\n        setAuthState({\n          user,\n          loading: false,\n          error: null\n        });\n      } else {\n        setAuthState({\n          user: null,\n          loading: false,\n          error: null\n        });\n      }\n    });\n    return unsubscribe;\n  }, []);\n  const signIn = async (email, password) => {\n    setAuthState(prev => ({\n      ...prev,\n      loading: true,\n      error: null\n    }));\n    const result = await authService.signIn(email, password);\n    if (result.success) {\n      setAuthState(prev => ({\n        ...prev,\n        loading: false\n      }));\n    } else {\n      setAuthState(prev => ({\n        ...prev,\n        loading: false,\n        error: result.error || 'Sign in failed'\n      }));\n    }\n    return result;\n  };\n  const register = async (email, password, displayName) => {\n    setAuthState(prev => ({\n      ...prev,\n      loading: true,\n      error: null\n    }));\n    const result = await authService.register(email, password, displayName);\n    if (result.success) {\n      setAuthState(prev => ({\n        ...prev,\n        loading: false\n      }));\n    } else {\n      setAuthState(prev => ({\n        ...prev,\n        loading: false,\n        error: result.error || 'Registration failed'\n      }));\n    }\n    return result;\n  };\n  const signInWithGoogle = async () => {\n    setAuthState(prev => ({\n      ...prev,\n      loading: true,\n      error: null\n    }));\n    const result = await authService.signInWithGoogle();\n    if (result.success) {\n      setAuthState(prev => ({\n        ...prev,\n        loading: false\n      }));\n    } else {\n      setAuthState(prev => ({\n        ...prev,\n        loading: false,\n        error: result.error || 'Google sign in failed'\n      }));\n    }\n    return result;\n  };\n  const signOut = async () => {\n    setAuthState(prev => ({\n      ...prev,\n      loading: true,\n      error: null\n    }));\n    const result = await authService.signOut();\n    setAuthState(prev => ({\n      ...prev,\n      loading: false\n    }));\n    return result;\n  };\n  const resetPassword = async email => {\n    setAuthState(prev => ({\n      ...prev,\n      error: null\n    }));\n    return await authService.resetPassword(email);\n  };\n  const sendEmailVerification = async () => {\n    setAuthState(prev => ({\n      ...prev,\n      error: null\n    }));\n    return await authService.sendEmailVerification();\n  };\n  const updateProfile = async updates => {\n    setAuthState(prev => ({\n      ...prev,\n      error: null\n    }));\n    const result = await authService.updateProfile(updates);\n    if (result.success) {\n      // Update local state\n      const updatedUser = authService.getCurrentUser();\n      setAuthState(prev => ({\n        ...prev,\n        user: updatedUser\n      }));\n    }\n    return result;\n  };\n  const clearError = () => {\n    setAuthState(prev => ({\n      ...prev,\n      error: null\n    }));\n  };\n  const value = {\n    ...authState,\n    signIn,\n    register,\n    signInWithGoogle,\n    signOut,\n    resetPassword,\n    sendEmailVerification,\n    updateProfile,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"kSEAe0+tPAwC2DQ8O6M1PNBHla8=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "authState", "setAuthState", "user", "loading", "error", "unsubscribe", "onAuthStateChanged", "firebaseUser", "getCurrentUser", "signIn", "email", "password", "prev", "result", "success", "register", "displayName", "signInWithGoogle", "signOut", "resetPassword", "sendEmailVerification", "updateProfile", "updates", "updatedUser", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { AuthState } from '../types';\nimport { authService } from '../services/firebase/auth';\n\ninterface AuthContextType extends AuthState {\n  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n  register: (email: string, password: string, displayName?: string) => Promise<{ success: boolean; error?: string }>;\n  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;\n  signOut: () => Promise<{ success: boolean; error?: string }>;\n  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;\n  sendEmailVerification: () => Promise<{ success: boolean; error?: string }>;\n  updateProfile: (updates: { displayName?: string; photoURL?: string }) => Promise<{ success: boolean; error?: string }>;\n  clearError: () => void;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    loading: true,\n    error: null,\n  });\n\n  useEffect(() => {\n    const unsubscribe = authService.onAuthStateChanged((firebaseUser) => {\n      if (firebaseUser) {\n        const user = authService.getCurrentUser();\n        setAuthState({\n          user,\n          loading: false,\n          error: null,\n        });\n      } else {\n        setAuthState({\n          user: null,\n          loading: false,\n          error: null,\n        });\n      }\n    });\n\n    return unsubscribe;\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    setAuthState(prev => ({ ...prev, loading: true, error: null }));\n    \n    const result = await authService.signIn(email, password);\n    \n    if (result.success) {\n      setAuthState(prev => ({ ...prev, loading: false }));\n    } else {\n      setAuthState(prev => ({ \n        ...prev, \n        loading: false, \n        error: result.error || 'Sign in failed' \n      }));\n    }\n    \n    return result;\n  };\n\n  const register = async (email: string, password: string, displayName?: string) => {\n    setAuthState(prev => ({ ...prev, loading: true, error: null }));\n    \n    const result = await authService.register(email, password, displayName);\n    \n    if (result.success) {\n      setAuthState(prev => ({ ...prev, loading: false }));\n    } else {\n      setAuthState(prev => ({ \n        ...prev, \n        loading: false, \n        error: result.error || 'Registration failed' \n      }));\n    }\n    \n    return result;\n  };\n\n  const signInWithGoogle = async () => {\n    setAuthState(prev => ({ ...prev, loading: true, error: null }));\n    \n    const result = await authService.signInWithGoogle();\n    \n    if (result.success) {\n      setAuthState(prev => ({ ...prev, loading: false }));\n    } else {\n      setAuthState(prev => ({ \n        ...prev, \n        loading: false, \n        error: result.error || 'Google sign in failed' \n      }));\n    }\n    \n    return result;\n  };\n\n  const signOut = async () => {\n    setAuthState(prev => ({ ...prev, loading: true, error: null }));\n    \n    const result = await authService.signOut();\n    \n    setAuthState(prev => ({ ...prev, loading: false }));\n    \n    return result;\n  };\n\n  const resetPassword = async (email: string) => {\n    setAuthState(prev => ({ ...prev, error: null }));\n    return await authService.resetPassword(email);\n  };\n\n  const sendEmailVerification = async () => {\n    setAuthState(prev => ({ ...prev, error: null }));\n    return await authService.sendEmailVerification();\n  };\n\n  const updateProfile = async (updates: { displayName?: string; photoURL?: string }) => {\n    setAuthState(prev => ({ ...prev, error: null }));\n    \n    const result = await authService.updateProfile(updates);\n    \n    if (result.success) {\n      // Update local state\n      const updatedUser = authService.getCurrentUser();\n      setAuthState(prev => ({ ...prev, user: updatedUser }));\n    }\n    \n    return result;\n  };\n\n  const clearError = () => {\n    setAuthState(prev => ({ ...prev, error: null }));\n  };\n\n  const value: AuthContextType = {\n    ...authState,\n    signIn,\n    register,\n    signInWithGoogle,\n    signOut,\n    resetPassword,\n    sendEmailVerification,\n    updateProfile,\n    clearError,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE7E,SAASC,WAAW,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAaxD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAY;IACpDe,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFlB,SAAS,CAAC,MAAM;IACd,MAAMmB,WAAW,GAAGjB,WAAW,CAACkB,kBAAkB,CAAEC,YAAY,IAAK;MACnE,IAAIA,YAAY,EAAE;QAChB,MAAML,IAAI,GAAGd,WAAW,CAACoB,cAAc,CAAC,CAAC;QACzCP,YAAY,CAAC;UACXC,IAAI;UACJC,OAAO,EAAE,KAAK;UACdC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACLH,YAAY,CAAC;UACXC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,KAAK;UACdC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOC,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,MAAM,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAK;IACxDV,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAE/D,MAAMS,MAAM,GAAG,MAAMzB,WAAW,CAACqB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC;IAExD,IAAIE,MAAM,CAACC,OAAO,EAAE;MAClBb,YAAY,CAACW,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAET,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM;MACLF,YAAY,CAACW,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPT,OAAO,EAAE,KAAK;QACdC,KAAK,EAAES,MAAM,CAACT,KAAK,IAAI;MACzB,CAAC,CAAC,CAAC;IACL;IAEA,OAAOS,MAAM;EACf,CAAC;EAED,MAAME,QAAQ,GAAG,MAAAA,CAAOL,KAAa,EAAEC,QAAgB,EAAEK,WAAoB,KAAK;IAChFf,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAE/D,MAAMS,MAAM,GAAG,MAAMzB,WAAW,CAAC2B,QAAQ,CAACL,KAAK,EAAEC,QAAQ,EAAEK,WAAW,CAAC;IAEvE,IAAIH,MAAM,CAACC,OAAO,EAAE;MAClBb,YAAY,CAACW,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAET,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM;MACLF,YAAY,CAACW,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPT,OAAO,EAAE,KAAK;QACdC,KAAK,EAAES,MAAM,CAACT,KAAK,IAAI;MACzB,CAAC,CAAC,CAAC;IACL;IAEA,OAAOS,MAAM;EACf,CAAC;EAED,MAAMI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnChB,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAE/D,MAAMS,MAAM,GAAG,MAAMzB,WAAW,CAAC6B,gBAAgB,CAAC,CAAC;IAEnD,IAAIJ,MAAM,CAACC,OAAO,EAAE;MAClBb,YAAY,CAACW,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAET,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM;MACLF,YAAY,CAACW,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPT,OAAO,EAAE,KAAK;QACdC,KAAK,EAAES,MAAM,CAACT,KAAK,IAAI;MACzB,CAAC,CAAC,CAAC;IACL;IAEA,OAAOS,MAAM;EACf,CAAC;EAED,MAAMK,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1BjB,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAE/D,MAAMS,MAAM,GAAG,MAAMzB,WAAW,CAAC8B,OAAO,CAAC,CAAC;IAE1CjB,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET,OAAO,EAAE;IAAM,CAAC,CAAC,CAAC;IAEnD,OAAOU,MAAM;EACf,CAAC;EAED,MAAMM,aAAa,GAAG,MAAOT,KAAa,IAAK;IAC7CT,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAChD,OAAO,MAAMhB,WAAW,CAAC+B,aAAa,CAACT,KAAK,CAAC;EAC/C,CAAC;EAED,MAAMU,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCnB,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAChD,OAAO,MAAMhB,WAAW,CAACgC,qBAAqB,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,aAAa,GAAG,MAAOC,OAAoD,IAAK;IACpFrB,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAEhD,MAAMS,MAAM,GAAG,MAAMzB,WAAW,CAACiC,aAAa,CAACC,OAAO,CAAC;IAEvD,IAAIT,MAAM,CAACC,OAAO,EAAE;MAClB;MACA,MAAMS,WAAW,GAAGnC,WAAW,CAACoB,cAAc,CAAC,CAAC;MAChDP,YAAY,CAACW,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEV,IAAI,EAAEqB;MAAY,CAAC,CAAC,CAAC;IACxD;IAEA,OAAOV,MAAM;EACf,CAAC;EAED,MAAMW,UAAU,GAAGA,CAAA,KAAM;IACvBvB,YAAY,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMqB,KAAsB,GAAG;IAC7B,GAAGzB,SAAS;IACZS,MAAM;IACNM,QAAQ;IACRE,gBAAgB;IAChBC,OAAO;IACPC,aAAa;IACbC,qBAAqB;IACrBC,aAAa;IACbG;EACF,CAAC;EAED,oBAAOlC,OAAA,CAACC,WAAW,CAACmC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3B,QAAA,EAAEA;EAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAAC/B,GAAA,CArIWF,YAAyC;AAAAkC,EAAA,GAAzClC,YAAyC;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}