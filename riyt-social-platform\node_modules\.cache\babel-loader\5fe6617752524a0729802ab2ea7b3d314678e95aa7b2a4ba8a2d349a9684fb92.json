{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getQuarter} function options.\n */\n\n/**\n * @name getQuarter\n * @category Quarter Helpers\n * @summary Get the year quarter of the given date.\n *\n * @description\n * Get the year quarter of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The quarter\n *\n * @example\n * // Which quarter is 2 July 2014?\n * const result = getQuarter(new Date(2014, 6, 2));\n * //=> 3\n */\nexport function getQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const quarter = Math.trunc(_date.getMonth() / 3) + 1;\n  return quarter;\n}\n\n// Fallback for modularized imports:\nexport default getQuarter;", "map": {"version": 3, "names": ["toDate", "getQuarter", "date", "options", "_date", "in", "quarter", "Math", "trunc", "getMonth"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/getQuarter.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getQuarter} function options.\n */\n\n/**\n * @name getQuarter\n * @category Quarter Helpers\n * @summary Get the year quarter of the given date.\n *\n * @description\n * Get the year quarter of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The quarter\n *\n * @example\n * // Which quarter is 2 July 2014?\n * const result = getQuarter(new Date(2014, 6, 2));\n * //=> 3\n */\nexport function getQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const quarter = Math.trunc(_date.getMonth() / 3) + 1;\n  return quarter;\n}\n\n// Fallback for modularized imports:\nexport default getQuarter;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACpD,OAAOH,OAAO;AAChB;;AAEA;AACA,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}