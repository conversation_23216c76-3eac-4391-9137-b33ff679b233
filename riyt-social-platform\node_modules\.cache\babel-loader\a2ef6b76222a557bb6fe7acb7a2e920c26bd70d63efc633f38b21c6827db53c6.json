{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { createSelector } from 'reselect';\nimport { selectAxisWithScale, selectCartesianAxisSize, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getPercentValue, isNullish } from '../../util/DataUtils';\nimport { getBandSizeOfAxis } from '../../util/ChartUtils';\nimport { computeBarRectangles } from '../../cartesian/Bar';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBarCategoryGap, selectBarGap, selectRootBarSize, selectRootMaxBarSize } from './rootPropsSelectors';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nvar pickXAxisId = (_state, xAxisId) => xAxisId;\nvar pickYAxisId = (_state, _xAxisId, yAxisId) => yAxisId;\nvar pickIsPanorama = (_state, _xAxisId, _yAxisId, isPanorama) => isPanorama;\nvar pickBarSettings = (_state, _xAxisId, _yAxisId, _isPanorama, barSettings) => barSettings;\nvar pickMaxBarSize = (_state, _xAxisId, _yAxisId, _isPanorama, barSettings) => barSettings.maxBarSize;\nvar pickCells = (_state, _xAxisId, _yAxisId, _isPanorama, _barSettings, cells) => cells;\nvar getBarSize = (globalSize, totalSize, selfSize) => {\n  var barSize = selfSize !== null && selfSize !== void 0 ? selfSize : globalSize;\n  if (isNullish(barSize)) {\n    return undefined;\n  }\n  return getPercentValue(barSize, totalSize, 0);\n};\nexport var selectAllVisibleBars = createSelector([selectChartLayout, selectUnfilteredCartesianItems, pickXAxisId, pickYAxisId, pickIsPanorama], (layout, allItems, xAxisId, yAxisId, isPanorama) => allItems.filter(i => {\n  if (layout === 'horizontal') {\n    return i.xAxisId === xAxisId;\n  }\n  return i.yAxisId === yAxisId;\n}).filter(i => i.isPanorama === isPanorama).filter(i => i.hide === false).filter(i => i.type === 'bar'));\nvar selectBarStackGroups = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n};\nexport var selectBarCartesianAxisSize = (state, xAxisId, yAxisId) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectCartesianAxisSize(state, 'xAxis', xAxisId);\n  }\n  return selectCartesianAxisSize(state, 'yAxis', yAxisId);\n};\n\n/**\n * Some graphical items allow data stacking. The stacks are optional,\n * so all props here are optional too.\n */\n\n/**\n * Some graphical items allow data stacking.\n * This interface is used to represent the items that are stacked\n * because the user has provided the stackId and dataKey properties.\n */\n\nfunction isStacked(graphicalItem) {\n  return graphicalItem.stackId != null && graphicalItem.dataKey != null;\n}\nexport var combineBarSizeList = (allBars, globalSize, totalSize) => {\n  var initialValue = {};\n  var stackedBars = allBars.filter(isStacked);\n  var unstackedBars = allBars.filter(b => b.stackId == null);\n  var groupByStack = stackedBars.reduce((acc, bar) => {\n    if (!acc[bar.stackId]) {\n      acc[bar.stackId] = [];\n    }\n    acc[bar.stackId].push(bar);\n    return acc;\n  }, initialValue);\n  var stackedSizeList = Object.entries(groupByStack).map(_ref => {\n    var [stackId, bars] = _ref;\n    var dataKeys = bars.map(b => b.dataKey);\n    var barSize = getBarSize(globalSize, totalSize, bars[0].barSize);\n    return {\n      stackId,\n      dataKeys,\n      barSize\n    };\n  });\n  var unstackedSizeList = unstackedBars.map(b => {\n    var dataKeys = [b.dataKey].filter(dk => dk != null);\n    var barSize = getBarSize(globalSize, totalSize, b.barSize);\n    return {\n      stackId: undefined,\n      dataKeys,\n      barSize\n    };\n  });\n  return [...stackedSizeList, ...unstackedSizeList];\n};\nexport var selectBarSizeList = createSelector([selectAllVisibleBars, selectRootBarSize, selectBarCartesianAxisSize], combineBarSizeList);\nexport var selectBarBandSize = (state, xAxisId, yAxisId, isPanorama, barSettings) => {\n  var _ref2, _getBandSizeOfAxis;\n  var layout = selectChartLayout(state);\n  var globalMaxBarSize = selectRootMaxBarSize(state);\n  var {\n    maxBarSize: childMaxBarSize\n  } = barSettings;\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return (_ref2 = (_getBandSizeOfAxis = getBandSizeOfAxis(axis, ticks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref2 !== void 0 ? _ref2 : 0;\n};\nvar selectAxisBandSize = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return getBandSizeOfAxis(axis, ticks);\n};\nfunction getBarPositions(barGap, barCategoryGap, bandSize, sizeList, maxBarSize) {\n  var len = sizeList.length;\n  if (len < 1) {\n    return undefined;\n  }\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n  var initialValue = [];\n\n  // whether is barSize set by user\n  // Okay but why does it check only for the first element? What if the first element is set but others are not?\n  if (isWellBehavedNumber(sizeList[0].barSize)) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    var sum = sizeList.reduce((res, entry) => res + (entry.barSize || 0), 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce((res, entry) => {\n      var _entry$barSize;\n      var newPosition = {\n        stackId: entry.stackId,\n        dataKeys: entry.dataKeys,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          size: useFull ? fullBarSize : (_entry$barSize = entry.barSize) !== null && _entry$barSize !== void 0 ? _entry$barSize : 0\n        }\n      };\n      var newRes = [...res, newPosition];\n      prev = newRes[newRes.length - 1].position;\n      return newRes;\n    }, initialValue);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = isWellBehavedNumber(maxBarSize) ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce((res, entry, i) => [...res, {\n      stackId: entry.stackId,\n      dataKeys: entry.dataKeys,\n      position: {\n        offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n        size\n      }\n    }], initialValue);\n  }\n  return result;\n}\nexport var combineAllBarPositions = (sizeList, globalMaxBarSize, barGap, barCategoryGap, barBandSize, bandSize, childMaxBarSize) => {\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var allBarPositions = getBarPositions(barGap, barCategoryGap, barBandSize !== bandSize ? barBandSize : bandSize, sizeList, maxBarSize);\n  if (barBandSize !== bandSize && allBarPositions != null) {\n    allBarPositions = allBarPositions.map(pos => _objectSpread(_objectSpread({}, pos), {}, {\n      position: _objectSpread(_objectSpread({}, pos.position), {}, {\n        offset: pos.position.offset - barBandSize / 2\n      })\n    }));\n  }\n  return allBarPositions;\n};\nexport var selectAllBarPositions = createSelector([selectBarSizeList, selectRootMaxBarSize, selectBarGap, selectBarCategoryGap, selectBarBandSize, selectAxisBandSize, pickMaxBarSize], combineAllBarPositions);\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nexport var selectBarPosition = createSelector([selectAllBarPositions, pickBarSettings], (allBarPositions, barSettings) => {\n  if (allBarPositions == null) {\n    return undefined;\n  }\n  var position = allBarPositions.find(p => p.stackId === barSettings.stackId && p.dataKeys.includes(barSettings.dataKey));\n  if (position == null) {\n    return undefined;\n  }\n  return position.position;\n});\nexport var combineStackedData = (stackGroups, barSettings) => {\n  if (!stackGroups || (barSettings === null || barSettings === void 0 ? void 0 : barSettings.dataKey) == null) {\n    return undefined;\n  }\n  var {\n    stackId\n  } = barSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var stackGroup = stackGroups[stackId];\n  if (!stackGroup) {\n    return undefined;\n  }\n  var {\n    stackedData\n  } = stackGroup;\n  if (!stackedData) {\n    return undefined;\n  }\n  var stack = stackedData.find(sd => sd.key === barSettings.dataKey);\n  return stack;\n};\nvar selectSynchronisedBarSettings = createSelector([selectUnfilteredCartesianItems, pickBarSettings], (graphicalItems, barSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'bar' && barSettingsFromProps.dataKey === cgis.dataKey && barSettingsFromProps.stackId === cgis.stackId &&\n  // barSettingsFromProps.data === cgis.data && // bar doesn't support data and one is undefined and another is null and this condition breaks\n  barSettingsFromProps.stackId === cgis.stackId)) {\n    return barSettingsFromProps;\n  }\n  return undefined;\n});\nvar selectStackedDataOfItem = createSelector([selectBarStackGroups, pickBarSettings], combineStackedData);\nexport var selectBarRectangles = createSelector([selectChartOffsetInternal, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectBarPosition, selectChartLayout, selectChartDataWithIndexesIfNotInPanorama, selectAxisBandSize, selectStackedDataOfItem, selectSynchronisedBarSettings, pickCells], (offset, xAxis, yAxis, xAxisTicks, yAxisTicks, pos, layout, _ref3, bandSize, stackedData, barSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (barSettings == null || pos == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = barSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeBarRectangles({\n    layout,\n    barSettings,\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    displayedData,\n    offset,\n    cells\n  });\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "createSelector", "selectAxisWithScale", "selectCartesianAxisSize", "selectStackGroups", "selectTicksOfGraphicalItem", "selectUnfilteredCartesianItems", "getPercentValue", "<PERSON><PERSON><PERSON><PERSON>", "getBandSizeOfAxis", "computeBarRectangles", "selectChartLayout", "selectChartDataWithIndexesIfNotInPanorama", "selectChartOffsetInternal", "selectBarCategoryGap", "selectBarGap", "selectRootBarSize", "selectRootMaxBarSize", "isWellBehavedNumber", "pickXAxisId", "_state", "xAxisId", "pickYAxisId", "_xAxisId", "yAxisId", "pickIsPanorama", "_yAxisId", "isPanorama", "pickBarSettings", "_isPanorama", "barSettings", "pickMaxBarSize", "maxBarSize", "pick<PERSON>ells", "_barSettings", "cells", "getBarSize", "globalSize", "totalSize", "selfSize", "barSize", "undefined", "selectAllVisibleBars", "layout", "allItems", "hide", "type", "selectBarStackGroups", "state", "selectBarCartesianAxisSize", "isStacked", "graphicalItem", "stackId", "dataKey", "combineBarSizeList", "allBars", "initialValue", "stackedBars", "unstackedBars", "b", "groupByStack", "reduce", "acc", "bar", "stackedSizeList", "entries", "map", "_ref", "bars", "dataKeys", "unstackedSizeList", "dk", "selectBarSizeList", "selectBarBandSize", "_ref2", "_getBandSizeOfAxis", "globalMaxBarSize", "childMaxBarSize", "axis", "ticks", "selectAxisBandSize", "getBarPositions", "barGap", "barCategoryGap", "bandSize", "sizeList", "len", "realBarGap", "result", "useFull", "fullBarSize", "sum", "res", "entry", "offset", "prev", "size", "_entry$barSize", "newPosition", "position", "newRes", "_offset", "originalSize", "Math", "min", "combineAllBarPositions", "barBandSize", "allBarPositions", "pos", "selectAllBarPositions", "selectXAxisWithScale", "selectYAxisWithScale", "selectXAxisTicks", "selectYAxisTicks", "selectBarPosition", "find", "p", "includes", "combineStackedData", "stackGroups", "stackGroup", "stackedData", "stack", "sd", "key", "selectSynchronisedBarSettings", "graphicalItems", "barSettingsFromProps", "some", "cgis", "selectStackedDataOfItem", "selectBarRectangles", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "_ref3", "chartData", "dataStartIndex", "dataEndIndex", "data", "displayedData", "slice"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/state/selectors/barSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport { selectAxisWithScale, selectCartesianAxisSize, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getPercentValue, isNullish } from '../../util/DataUtils';\nimport { getBandSizeOfAxis } from '../../util/ChartUtils';\nimport { computeBarRectangles } from '../../cartesian/Bar';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBarCategoryGap, selectBarGap, selectRootBarSize, selectRootMaxBarSize } from './rootPropsSelectors';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nvar pickXAxisId = (_state, xAxisId) => xAxisId;\nvar pickYAxisId = (_state, _xAxisId, yAxisId) => yAxisId;\nvar pickIsPanorama = (_state, _xAxisId, _yAxisId, isPanorama) => isPanorama;\nvar pickBarSettings = (_state, _xAxisId, _yAxisId, _isPanorama, barSettings) => barSettings;\nvar pickMaxBarSize = (_state, _xAxisId, _yAxisId, _isPanorama, barSettings) => barSettings.maxBarSize;\nvar pickCells = (_state, _xAxisId, _yAxisId, _isPanorama, _barSettings, cells) => cells;\nvar getBarSize = (globalSize, totalSize, selfSize) => {\n  var barSize = selfSize !== null && selfSize !== void 0 ? selfSize : globalSize;\n  if (isNullish(barSize)) {\n    return undefined;\n  }\n  return getPercentValue(barSize, totalSize, 0);\n};\nexport var selectAllVisibleBars = createSelector([selectChartLayout, selectUnfilteredCartesianItems, pickXAxisId, pickYAxisId, pickIsPanorama], (layout, allItems, xAxisId, yAxisId, isPanorama) => allItems.filter(i => {\n  if (layout === 'horizontal') {\n    return i.xAxisId === xAxisId;\n  }\n  return i.yAxisId === yAxisId;\n}).filter(i => i.isPanorama === isPanorama).filter(i => i.hide === false).filter(i => i.type === 'bar'));\nvar selectBarStackGroups = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n};\nexport var selectBarCartesianAxisSize = (state, xAxisId, yAxisId) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectCartesianAxisSize(state, 'xAxis', xAxisId);\n  }\n  return selectCartesianAxisSize(state, 'yAxis', yAxisId);\n};\n\n/**\n * Some graphical items allow data stacking. The stacks are optional,\n * so all props here are optional too.\n */\n\n/**\n * Some graphical items allow data stacking.\n * This interface is used to represent the items that are stacked\n * because the user has provided the stackId and dataKey properties.\n */\n\nfunction isStacked(graphicalItem) {\n  return graphicalItem.stackId != null && graphicalItem.dataKey != null;\n}\nexport var combineBarSizeList = (allBars, globalSize, totalSize) => {\n  var initialValue = {};\n  var stackedBars = allBars.filter(isStacked);\n  var unstackedBars = allBars.filter(b => b.stackId == null);\n  var groupByStack = stackedBars.reduce((acc, bar) => {\n    if (!acc[bar.stackId]) {\n      acc[bar.stackId] = [];\n    }\n    acc[bar.stackId].push(bar);\n    return acc;\n  }, initialValue);\n  var stackedSizeList = Object.entries(groupByStack).map(_ref => {\n    var [stackId, bars] = _ref;\n    var dataKeys = bars.map(b => b.dataKey);\n    var barSize = getBarSize(globalSize, totalSize, bars[0].barSize);\n    return {\n      stackId,\n      dataKeys,\n      barSize\n    };\n  });\n  var unstackedSizeList = unstackedBars.map(b => {\n    var dataKeys = [b.dataKey].filter(dk => dk != null);\n    var barSize = getBarSize(globalSize, totalSize, b.barSize);\n    return {\n      stackId: undefined,\n      dataKeys,\n      barSize\n    };\n  });\n  return [...stackedSizeList, ...unstackedSizeList];\n};\nexport var selectBarSizeList = createSelector([selectAllVisibleBars, selectRootBarSize, selectBarCartesianAxisSize], combineBarSizeList);\nexport var selectBarBandSize = (state, xAxisId, yAxisId, isPanorama, barSettings) => {\n  var _ref2, _getBandSizeOfAxis;\n  var layout = selectChartLayout(state);\n  var globalMaxBarSize = selectRootMaxBarSize(state);\n  var {\n    maxBarSize: childMaxBarSize\n  } = barSettings;\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return (_ref2 = (_getBandSizeOfAxis = getBandSizeOfAxis(axis, ticks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref2 !== void 0 ? _ref2 : 0;\n};\nvar selectAxisBandSize = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return getBandSizeOfAxis(axis, ticks);\n};\nfunction getBarPositions(barGap, barCategoryGap, bandSize, sizeList, maxBarSize) {\n  var len = sizeList.length;\n  if (len < 1) {\n    return undefined;\n  }\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n  var initialValue = [];\n\n  // whether is barSize set by user\n  // Okay but why does it check only for the first element? What if the first element is set but others are not?\n  if (isWellBehavedNumber(sizeList[0].barSize)) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    var sum = sizeList.reduce((res, entry) => res + (entry.barSize || 0), 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce((res, entry) => {\n      var _entry$barSize;\n      var newPosition = {\n        stackId: entry.stackId,\n        dataKeys: entry.dataKeys,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          size: useFull ? fullBarSize : (_entry$barSize = entry.barSize) !== null && _entry$barSize !== void 0 ? _entry$barSize : 0\n        }\n      };\n      var newRes = [...res, newPosition];\n      prev = newRes[newRes.length - 1].position;\n      return newRes;\n    }, initialValue);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = isWellBehavedNumber(maxBarSize) ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce((res, entry, i) => [...res, {\n      stackId: entry.stackId,\n      dataKeys: entry.dataKeys,\n      position: {\n        offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n        size\n      }\n    }], initialValue);\n  }\n  return result;\n}\nexport var combineAllBarPositions = (sizeList, globalMaxBarSize, barGap, barCategoryGap, barBandSize, bandSize, childMaxBarSize) => {\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var allBarPositions = getBarPositions(barGap, barCategoryGap, barBandSize !== bandSize ? barBandSize : bandSize, sizeList, maxBarSize);\n  if (barBandSize !== bandSize && allBarPositions != null) {\n    allBarPositions = allBarPositions.map(pos => _objectSpread(_objectSpread({}, pos), {}, {\n      position: _objectSpread(_objectSpread({}, pos.position), {}, {\n        offset: pos.position.offset - barBandSize / 2\n      })\n    }));\n  }\n  return allBarPositions;\n};\nexport var selectAllBarPositions = createSelector([selectBarSizeList, selectRootMaxBarSize, selectBarGap, selectBarCategoryGap, selectBarBandSize, selectAxisBandSize, pickMaxBarSize], combineAllBarPositions);\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nexport var selectBarPosition = createSelector([selectAllBarPositions, pickBarSettings], (allBarPositions, barSettings) => {\n  if (allBarPositions == null) {\n    return undefined;\n  }\n  var position = allBarPositions.find(p => p.stackId === barSettings.stackId && p.dataKeys.includes(barSettings.dataKey));\n  if (position == null) {\n    return undefined;\n  }\n  return position.position;\n});\nexport var combineStackedData = (stackGroups, barSettings) => {\n  if (!stackGroups || (barSettings === null || barSettings === void 0 ? void 0 : barSettings.dataKey) == null) {\n    return undefined;\n  }\n  var {\n    stackId\n  } = barSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var stackGroup = stackGroups[stackId];\n  if (!stackGroup) {\n    return undefined;\n  }\n  var {\n    stackedData\n  } = stackGroup;\n  if (!stackedData) {\n    return undefined;\n  }\n  var stack = stackedData.find(sd => sd.key === barSettings.dataKey);\n  return stack;\n};\nvar selectSynchronisedBarSettings = createSelector([selectUnfilteredCartesianItems, pickBarSettings], (graphicalItems, barSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'bar' && barSettingsFromProps.dataKey === cgis.dataKey && barSettingsFromProps.stackId === cgis.stackId &&\n  // barSettingsFromProps.data === cgis.data && // bar doesn't support data and one is undefined and another is null and this condition breaks\n  barSettingsFromProps.stackId === cgis.stackId)) {\n    return barSettingsFromProps;\n  }\n  return undefined;\n});\nvar selectStackedDataOfItem = createSelector([selectBarStackGroups, pickBarSettings], combineStackedData);\nexport var selectBarRectangles = createSelector([selectChartOffsetInternal, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectBarPosition, selectChartLayout, selectChartDataWithIndexesIfNotInPanorama, selectAxisBandSize, selectStackedDataOfItem, selectSynchronisedBarSettings, pickCells], (offset, xAxis, yAxis, xAxisTicks, yAxisTicks, pos, layout, _ref3, bandSize, stackedData, barSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (barSettings == null || pos == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = barSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeBarRectangles({\n    layout,\n    barSettings,\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    displayedData,\n    offset,\n    cells\n  });\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,cAAc,QAAQ,UAAU;AACzC,SAASC,mBAAmB,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,0BAA0B,EAAEC,8BAA8B,QAAQ,iBAAiB;AAC7J,SAASC,eAAe,EAAEC,SAAS,QAAQ,sBAAsB;AACjE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,yCAAyC,QAAQ,iBAAiB;AAC3E,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,oBAAoB,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,sBAAsB;AAClH,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,IAAIC,WAAW,GAAGA,CAACC,MAAM,EAAEC,OAAO,KAAKA,OAAO;AAC9C,IAAIC,WAAW,GAAGA,CAACF,MAAM,EAAEG,QAAQ,EAAEC,OAAO,KAAKA,OAAO;AACxD,IAAIC,cAAc,GAAGA,CAACL,MAAM,EAAEG,QAAQ,EAAEG,QAAQ,EAAEC,UAAU,KAAKA,UAAU;AAC3E,IAAIC,eAAe,GAAGA,CAACR,MAAM,EAAEG,QAAQ,EAAEG,QAAQ,EAAEG,WAAW,EAAEC,WAAW,KAAKA,WAAW;AAC3F,IAAIC,cAAc,GAAGA,CAACX,MAAM,EAAEG,QAAQ,EAAEG,QAAQ,EAAEG,WAAW,EAAEC,WAAW,KAAKA,WAAW,CAACE,UAAU;AACrG,IAAIC,SAAS,GAAGA,CAACb,MAAM,EAAEG,QAAQ,EAAEG,QAAQ,EAAEG,WAAW,EAAEK,YAAY,EAAEC,KAAK,KAAKA,KAAK;AACvF,IAAIC,UAAU,GAAGA,CAACC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,KAAK;EACpD,IAAIC,OAAO,GAAGD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGF,UAAU;EAC9E,IAAI7B,SAAS,CAACgC,OAAO,CAAC,EAAE;IACtB,OAAOC,SAAS;EAClB;EACA,OAAOlC,eAAe,CAACiC,OAAO,EAAEF,SAAS,EAAE,CAAC,CAAC;AAC/C,CAAC;AACD,OAAO,IAAII,oBAAoB,GAAGzC,cAAc,CAAC,CAACU,iBAAiB,EAAEL,8BAA8B,EAAEa,WAAW,EAAEG,WAAW,EAAEG,cAAc,CAAC,EAAE,CAACkB,MAAM,EAAEC,QAAQ,EAAEvB,OAAO,EAAEG,OAAO,EAAEG,UAAU,KAAKiB,QAAQ,CAACpE,MAAM,CAACiB,CAAC,IAAI;EACvN,IAAIkD,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAOlD,CAAC,CAAC4B,OAAO,KAAKA,OAAO;EAC9B;EACA,OAAO5B,CAAC,CAAC+B,OAAO,KAAKA,OAAO;AAC9B,CAAC,CAAC,CAAChD,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAACkC,UAAU,KAAKA,UAAU,CAAC,CAACnD,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAACoD,IAAI,KAAK,KAAK,CAAC,CAACrE,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAACqD,IAAI,KAAK,KAAK,CAAC,CAAC;AACxG,IAAIC,oBAAoB,GAAGA,CAACC,KAAK,EAAE3B,OAAO,EAAEG,OAAO,EAAEG,UAAU,KAAK;EAClE,IAAIgB,MAAM,GAAGhC,iBAAiB,CAACqC,KAAK,CAAC;EACrC,IAAIL,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAOvC,iBAAiB,CAAC4C,KAAK,EAAE,OAAO,EAAExB,OAAO,EAAEG,UAAU,CAAC;EAC/D;EACA,OAAOvB,iBAAiB,CAAC4C,KAAK,EAAE,OAAO,EAAE3B,OAAO,EAAEM,UAAU,CAAC;AAC/D,CAAC;AACD,OAAO,IAAIsB,0BAA0B,GAAGA,CAACD,KAAK,EAAE3B,OAAO,EAAEG,OAAO,KAAK;EACnE,IAAImB,MAAM,GAAGhC,iBAAiB,CAACqC,KAAK,CAAC;EACrC,IAAIL,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAOxC,uBAAuB,CAAC6C,KAAK,EAAE,OAAO,EAAE3B,OAAO,CAAC;EACzD;EACA,OAAOlB,uBAAuB,CAAC6C,KAAK,EAAE,OAAO,EAAExB,OAAO,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAAS0B,SAASA,CAACC,aAAa,EAAE;EAChC,OAAOA,aAAa,CAACC,OAAO,IAAI,IAAI,IAAID,aAAa,CAACE,OAAO,IAAI,IAAI;AACvE;AACA,OAAO,IAAIC,kBAAkB,GAAGA,CAACC,OAAO,EAAElB,UAAU,EAAEC,SAAS,KAAK;EAClE,IAAIkB,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIC,WAAW,GAAGF,OAAO,CAAC/E,MAAM,CAAC0E,SAAS,CAAC;EAC3C,IAAIQ,aAAa,GAAGH,OAAO,CAAC/E,MAAM,CAACmF,CAAC,IAAIA,CAAC,CAACP,OAAO,IAAI,IAAI,CAAC;EAC1D,IAAIQ,YAAY,GAAGH,WAAW,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAClD,IAAI,CAACD,GAAG,CAACC,GAAG,CAACX,OAAO,CAAC,EAAE;MACrBU,GAAG,CAACC,GAAG,CAACX,OAAO,CAAC,GAAG,EAAE;IACvB;IACAU,GAAG,CAACC,GAAG,CAACX,OAAO,CAAC,CAACzE,IAAI,CAACoF,GAAG,CAAC;IAC1B,OAAOD,GAAG;EACZ,CAAC,EAAEN,YAAY,CAAC;EAChB,IAAIQ,eAAe,GAAG5F,MAAM,CAAC6F,OAAO,CAACL,YAAY,CAAC,CAACM,GAAG,CAACC,IAAI,IAAI;IAC7D,IAAI,CAACf,OAAO,EAAEgB,IAAI,CAAC,GAAGD,IAAI;IAC1B,IAAIE,QAAQ,GAAGD,IAAI,CAACF,GAAG,CAACP,CAAC,IAAIA,CAAC,CAACN,OAAO,CAAC;IACvC,IAAIb,OAAO,GAAGJ,UAAU,CAACC,UAAU,EAAEC,SAAS,EAAE8B,IAAI,CAAC,CAAC,CAAC,CAAC5B,OAAO,CAAC;IAChE,OAAO;MACLY,OAAO;MACPiB,QAAQ;MACR7B;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAI8B,iBAAiB,GAAGZ,aAAa,CAACQ,GAAG,CAACP,CAAC,IAAI;IAC7C,IAAIU,QAAQ,GAAG,CAACV,CAAC,CAACN,OAAO,CAAC,CAAC7E,MAAM,CAAC+F,EAAE,IAAIA,EAAE,IAAI,IAAI,CAAC;IACnD,IAAI/B,OAAO,GAAGJ,UAAU,CAACC,UAAU,EAAEC,SAAS,EAAEqB,CAAC,CAACnB,OAAO,CAAC;IAC1D,OAAO;MACLY,OAAO,EAAEX,SAAS;MAClB4B,QAAQ;MACR7B;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO,CAAC,GAAGwB,eAAe,EAAE,GAAGM,iBAAiB,CAAC;AACnD,CAAC;AACD,OAAO,IAAIE,iBAAiB,GAAGvE,cAAc,CAAC,CAACyC,oBAAoB,EAAE1B,iBAAiB,EAAEiC,0BAA0B,CAAC,EAAEK,kBAAkB,CAAC;AACxI,OAAO,IAAImB,iBAAiB,GAAGA,CAACzB,KAAK,EAAE3B,OAAO,EAAEG,OAAO,EAAEG,UAAU,EAAEG,WAAW,KAAK;EACnF,IAAI4C,KAAK,EAAEC,kBAAkB;EAC7B,IAAIhC,MAAM,GAAGhC,iBAAiB,CAACqC,KAAK,CAAC;EACrC,IAAI4B,gBAAgB,GAAG3D,oBAAoB,CAAC+B,KAAK,CAAC;EAClD,IAAI;IACFhB,UAAU,EAAE6C;EACd,CAAC,GAAG/C,WAAW;EACf,IAAIE,UAAU,GAAGxB,SAAS,CAACqE,eAAe,CAAC,GAAGD,gBAAgB,GAAGC,eAAe;EAChF,IAAIC,IAAI,EAAEC,KAAK;EACf,IAAIpC,MAAM,KAAK,YAAY,EAAE;IAC3BmC,IAAI,GAAG5E,mBAAmB,CAAC8C,KAAK,EAAE,OAAO,EAAE3B,OAAO,EAAEM,UAAU,CAAC;IAC/DoD,KAAK,GAAG1E,0BAA0B,CAAC2C,KAAK,EAAE,OAAO,EAAE3B,OAAO,EAAEM,UAAU,CAAC;EACzE,CAAC,MAAM;IACLmD,IAAI,GAAG5E,mBAAmB,CAAC8C,KAAK,EAAE,OAAO,EAAExB,OAAO,EAAEG,UAAU,CAAC;IAC/DoD,KAAK,GAAG1E,0BAA0B,CAAC2C,KAAK,EAAE,OAAO,EAAExB,OAAO,EAAEG,UAAU,CAAC;EACzE;EACA,OAAO,CAAC+C,KAAK,GAAG,CAACC,kBAAkB,GAAGlE,iBAAiB,CAACqE,IAAI,EAAEC,KAAK,EAAE,IAAI,CAAC,MAAM,IAAI,IAAIJ,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG3C,UAAU,MAAM,IAAI,IAAI0C,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;AACnM,CAAC;AACD,IAAIM,kBAAkB,GAAGA,CAAChC,KAAK,EAAE3B,OAAO,EAAEG,OAAO,EAAEG,UAAU,KAAK;EAChE,IAAIgB,MAAM,GAAGhC,iBAAiB,CAACqC,KAAK,CAAC;EACrC,IAAI8B,IAAI,EAAEC,KAAK;EACf,IAAIpC,MAAM,KAAK,YAAY,EAAE;IAC3BmC,IAAI,GAAG5E,mBAAmB,CAAC8C,KAAK,EAAE,OAAO,EAAE3B,OAAO,EAAEM,UAAU,CAAC;IAC/DoD,KAAK,GAAG1E,0BAA0B,CAAC2C,KAAK,EAAE,OAAO,EAAE3B,OAAO,EAAEM,UAAU,CAAC;EACzE,CAAC,MAAM;IACLmD,IAAI,GAAG5E,mBAAmB,CAAC8C,KAAK,EAAE,OAAO,EAAExB,OAAO,EAAEG,UAAU,CAAC;IAC/DoD,KAAK,GAAG1E,0BAA0B,CAAC2C,KAAK,EAAE,OAAO,EAAExB,OAAO,EAAEG,UAAU,CAAC;EACzE;EACA,OAAOlB,iBAAiB,CAACqE,IAAI,EAAEC,KAAK,CAAC;AACvC,CAAC;AACD,SAASE,eAAeA,CAACC,MAAM,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,QAAQ,EAAErD,UAAU,EAAE;EAC/E,IAAIsD,GAAG,GAAGD,QAAQ,CAACtG,MAAM;EACzB,IAAIuG,GAAG,GAAG,CAAC,EAAE;IACX,OAAO7C,SAAS;EAClB;EACA,IAAI8C,UAAU,GAAGhF,eAAe,CAAC2E,MAAM,EAAEE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;EAC3D,IAAII,MAAM;EACV,IAAIhC,YAAY,GAAG,EAAE;;EAErB;EACA;EACA,IAAItC,mBAAmB,CAACmE,QAAQ,CAAC,CAAC,CAAC,CAAC7C,OAAO,CAAC,EAAE;IAC5C,IAAIiD,OAAO,GAAG,KAAK;IACnB,IAAIC,WAAW,GAAGN,QAAQ,GAAGE,GAAG;IAChC,IAAIK,GAAG,GAAGN,QAAQ,CAACxB,MAAM,CAAC,CAAC+B,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAIC,KAAK,CAACrD,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACxEmD,GAAG,IAAI,CAACL,GAAG,GAAG,CAAC,IAAIC,UAAU;IAC7B,IAAII,GAAG,IAAIP,QAAQ,EAAE;MACnBO,GAAG,IAAI,CAACL,GAAG,GAAG,CAAC,IAAIC,UAAU;MAC7BA,UAAU,GAAG,CAAC;IAChB;IACA,IAAII,GAAG,IAAIP,QAAQ,IAAIM,WAAW,GAAG,CAAC,EAAE;MACtCD,OAAO,GAAG,IAAI;MACdC,WAAW,IAAI,GAAG;MAClBC,GAAG,GAAGL,GAAG,GAAGI,WAAW;IACzB;IACA,IAAII,MAAM,GAAG,CAACV,QAAQ,GAAGO,GAAG,IAAI,CAAC,IAAI,CAAC;IACtC,IAAII,IAAI,GAAG;MACTD,MAAM,EAAEA,MAAM,GAAGP,UAAU;MAC3BS,IAAI,EAAE;IACR,CAAC;IACDR,MAAM,GAAGH,QAAQ,CAACxB,MAAM,CAAC,CAAC+B,GAAG,EAAEC,KAAK,KAAK;MACvC,IAAII,cAAc;MAClB,IAAIC,WAAW,GAAG;QAChB9C,OAAO,EAAEyC,KAAK,CAACzC,OAAO;QACtBiB,QAAQ,EAAEwB,KAAK,CAACxB,QAAQ;QACxB8B,QAAQ,EAAE;UACRL,MAAM,EAAEC,IAAI,CAACD,MAAM,GAAGC,IAAI,CAACC,IAAI,GAAGT,UAAU;UAC5CS,IAAI,EAAEP,OAAO,GAAGC,WAAW,GAAG,CAACO,cAAc,GAAGJ,KAAK,CAACrD,OAAO,MAAM,IAAI,IAAIyD,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG;QAC1H;MACF,CAAC;MACD,IAAIG,MAAM,GAAG,CAAC,GAAGR,GAAG,EAAEM,WAAW,CAAC;MAClCH,IAAI,GAAGK,MAAM,CAACA,MAAM,CAACrH,MAAM,GAAG,CAAC,CAAC,CAACoH,QAAQ;MACzC,OAAOC,MAAM;IACf,CAAC,EAAE5C,YAAY,CAAC;EAClB,CAAC,MAAM;IACL,IAAI6C,OAAO,GAAG9F,eAAe,CAAC4E,cAAc,EAAEC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;IAChE,IAAIA,QAAQ,GAAG,CAAC,GAAGiB,OAAO,GAAG,CAACf,GAAG,GAAG,CAAC,IAAIC,UAAU,IAAI,CAAC,EAAE;MACxDA,UAAU,GAAG,CAAC;IAChB;IACA,IAAIe,YAAY,GAAG,CAAClB,QAAQ,GAAG,CAAC,GAAGiB,OAAO,GAAG,CAACf,GAAG,GAAG,CAAC,IAAIC,UAAU,IAAID,GAAG;IAC1E,IAAIgB,YAAY,GAAG,CAAC,EAAE;MACpBA,YAAY,KAAK,CAAC;IACpB;IACA,IAAIN,IAAI,GAAG9E,mBAAmB,CAACc,UAAU,CAAC,GAAGuE,IAAI,CAACC,GAAG,CAACF,YAAY,EAAEtE,UAAU,CAAC,GAAGsE,YAAY;IAC9Fd,MAAM,GAAGH,QAAQ,CAACxB,MAAM,CAAC,CAAC+B,GAAG,EAAEC,KAAK,EAAEpG,CAAC,KAAK,CAAC,GAAGmG,GAAG,EAAE;MACnDxC,OAAO,EAAEyC,KAAK,CAACzC,OAAO;MACtBiB,QAAQ,EAAEwB,KAAK,CAACxB,QAAQ;MACxB8B,QAAQ,EAAE;QACRL,MAAM,EAAEO,OAAO,GAAG,CAACC,YAAY,GAAGf,UAAU,IAAI9F,CAAC,GAAG,CAAC6G,YAAY,GAAGN,IAAI,IAAI,CAAC;QAC7EA;MACF;IACF,CAAC,CAAC,EAAExC,YAAY,CAAC;EACnB;EACA,OAAOgC,MAAM;AACf;AACA,OAAO,IAAIiB,sBAAsB,GAAGA,CAACpB,QAAQ,EAAET,gBAAgB,EAAEM,MAAM,EAAEC,cAAc,EAAEuB,WAAW,EAAEtB,QAAQ,EAAEP,eAAe,KAAK;EAClI,IAAI7C,UAAU,GAAGxB,SAAS,CAACqE,eAAe,CAAC,GAAGD,gBAAgB,GAAGC,eAAe;EAChF,IAAI8B,eAAe,GAAG1B,eAAe,CAACC,MAAM,EAAEC,cAAc,EAAEuB,WAAW,KAAKtB,QAAQ,GAAGsB,WAAW,GAAGtB,QAAQ,EAAEC,QAAQ,EAAErD,UAAU,CAAC;EACtI,IAAI0E,WAAW,KAAKtB,QAAQ,IAAIuB,eAAe,IAAI,IAAI,EAAE;IACvDA,eAAe,GAAGA,eAAe,CAACzC,GAAG,CAAC0C,GAAG,IAAI/H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+H,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;MACrFT,QAAQ,EAAEtH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+H,GAAG,CAACT,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3DL,MAAM,EAAEc,GAAG,CAACT,QAAQ,CAACL,MAAM,GAAGY,WAAW,GAAG;MAC9C,CAAC;IACH,CAAC,CAAC,CAAC;EACL;EACA,OAAOC,eAAe;AACxB,CAAC;AACD,OAAO,IAAIE,qBAAqB,GAAG5G,cAAc,CAAC,CAACuE,iBAAiB,EAAEvD,oBAAoB,EAAEF,YAAY,EAAED,oBAAoB,EAAE2D,iBAAiB,EAAEO,kBAAkB,EAAEjD,cAAc,CAAC,EAAE0E,sBAAsB,CAAC;AAC/M,IAAIK,oBAAoB,GAAGA,CAAC9D,KAAK,EAAE3B,OAAO,EAAEK,QAAQ,EAAEC,UAAU,KAAKzB,mBAAmB,CAAC8C,KAAK,EAAE,OAAO,EAAE3B,OAAO,EAAEM,UAAU,CAAC;AAC7H,IAAIoF,oBAAoB,GAAGA,CAAC/D,KAAK,EAAEzB,QAAQ,EAAEC,OAAO,EAAEG,UAAU,KAAKzB,mBAAmB,CAAC8C,KAAK,EAAE,OAAO,EAAExB,OAAO,EAAEG,UAAU,CAAC;AAC7H,IAAIqF,gBAAgB,GAAGA,CAAChE,KAAK,EAAE3B,OAAO,EAAEK,QAAQ,EAAEC,UAAU,KAAKtB,0BAA0B,CAAC2C,KAAK,EAAE,OAAO,EAAE3B,OAAO,EAAEM,UAAU,CAAC;AAChI,IAAIsF,gBAAgB,GAAGA,CAACjE,KAAK,EAAEzB,QAAQ,EAAEC,OAAO,EAAEG,UAAU,KAAKtB,0BAA0B,CAAC2C,KAAK,EAAE,OAAO,EAAExB,OAAO,EAAEG,UAAU,CAAC;AAChI,OAAO,IAAIuF,iBAAiB,GAAGjH,cAAc,CAAC,CAAC4G,qBAAqB,EAAEjF,eAAe,CAAC,EAAE,CAAC+E,eAAe,EAAE7E,WAAW,KAAK;EACxH,IAAI6E,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAOlE,SAAS;EAClB;EACA,IAAI0D,QAAQ,GAAGQ,eAAe,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChE,OAAO,KAAKtB,WAAW,CAACsB,OAAO,IAAIgE,CAAC,CAAC/C,QAAQ,CAACgD,QAAQ,CAACvF,WAAW,CAACuB,OAAO,CAAC,CAAC;EACvH,IAAI8C,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAO1D,SAAS;EAClB;EACA,OAAO0D,QAAQ,CAACA,QAAQ;AAC1B,CAAC,CAAC;AACF,OAAO,IAAImB,kBAAkB,GAAGA,CAACC,WAAW,EAAEzF,WAAW,KAAK;EAC5D,IAAI,CAACyF,WAAW,IAAI,CAACzF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACuB,OAAO,KAAK,IAAI,EAAE;IAC3G,OAAOZ,SAAS;EAClB;EACA,IAAI;IACFW;EACF,CAAC,GAAGtB,WAAW;EACf,IAAIsB,OAAO,IAAI,IAAI,EAAE;IACnB,OAAOX,SAAS;EAClB;EACA,IAAI+E,UAAU,GAAGD,WAAW,CAACnE,OAAO,CAAC;EACrC,IAAI,CAACoE,UAAU,EAAE;IACf,OAAO/E,SAAS;EAClB;EACA,IAAI;IACFgF;EACF,CAAC,GAAGD,UAAU;EACd,IAAI,CAACC,WAAW,EAAE;IAChB,OAAOhF,SAAS;EAClB;EACA,IAAIiF,KAAK,GAAGD,WAAW,CAACN,IAAI,CAACQ,EAAE,IAAIA,EAAE,CAACC,GAAG,KAAK9F,WAAW,CAACuB,OAAO,CAAC;EAClE,OAAOqE,KAAK;AACd,CAAC;AACD,IAAIG,6BAA6B,GAAG5H,cAAc,CAAC,CAACK,8BAA8B,EAAEsB,eAAe,CAAC,EAAE,CAACkG,cAAc,EAAEC,oBAAoB,KAAK;EAC9I,IAAID,cAAc,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACnF,IAAI,KAAK,KAAK,IAAIiF,oBAAoB,CAAC1E,OAAO,KAAK4E,IAAI,CAAC5E,OAAO,IAAI0E,oBAAoB,CAAC3E,OAAO,KAAK6E,IAAI,CAAC7E,OAAO;EACrJ;EACA2E,oBAAoB,CAAC3E,OAAO,KAAK6E,IAAI,CAAC7E,OAAO,CAAC,EAAE;IAC9C,OAAO2E,oBAAoB;EAC7B;EACA,OAAOtF,SAAS;AAClB,CAAC,CAAC;AACF,IAAIyF,uBAAuB,GAAGjI,cAAc,CAAC,CAAC8C,oBAAoB,EAAEnB,eAAe,CAAC,EAAE0F,kBAAkB,CAAC;AACzG,OAAO,IAAIa,mBAAmB,GAAGlI,cAAc,CAAC,CAACY,yBAAyB,EAAEiG,oBAAoB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEvG,iBAAiB,EAAEC,yCAAyC,EAAEoE,kBAAkB,EAAEkD,uBAAuB,EAAEL,6BAA6B,EAAE5F,SAAS,CAAC,EAAE,CAAC6D,MAAM,EAAEsC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAE3B,GAAG,EAAEjE,MAAM,EAAE6F,KAAK,EAAEpD,QAAQ,EAAEqC,WAAW,EAAE3F,WAAW,EAAEK,KAAK,KAAK;EACpb,IAAI;IACFsG,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGH,KAAK;EACT,IAAI1G,WAAW,IAAI,IAAI,IAAI8E,GAAG,IAAI,IAAI,IAAIjE,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,IAAIyF,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAInD,QAAQ,IAAI,IAAI,EAAE;IAC5L,OAAO3C,SAAS;EAClB;EACA,IAAI;IACFmG;EACF,CAAC,GAAG9G,WAAW;EACf,IAAI+G,aAAa;EACjB,IAAID,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC7J,MAAM,GAAG,CAAC,EAAE;IACnC8J,aAAa,GAAGD,IAAI;EACtB,CAAC,MAAM;IACLC,aAAa,GAAGJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,KAAK,CAACJ,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACzH;EACA,IAAIE,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOpG,SAAS;EAClB;EACA,OAAO/B,oBAAoB,CAAC;IAC1BiC,MAAM;IACNb,WAAW;IACX8E,GAAG;IACHxB,QAAQ;IACRgD,KAAK;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVd,WAAW;IACXoB,aAAa;IACb/C,MAAM;IACN3D;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}