{"ast": null, "code": "import { createContext } from 'react';\nexport var BrushUpdateDispatchContext = /*#__PURE__*/createContext(() => {});", "map": {"version": 3, "names": ["createContext", "BrushUpdateDispatchContext"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/context/brushUpdateContext.js"], "sourcesContent": ["import { createContext } from 'react';\nexport var BrushUpdateDispatchContext = /*#__PURE__*/createContext(() => {});"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,OAAO,IAAIC,0BAA0B,GAAG,aAAaD,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}