{"ast": null, "code": "import pointRadial from \"../pointRadial.js\";\nclass Bump {\n  constructor(context, x) {\n    this._context = context;\n    this._x = x;\n  }\n  areaStart() {\n    this._line = 0;\n  }\n  areaEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  }\n  point(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        {\n          this._point = 1;\n          if (this._line) this._context.lineTo(x, y);else this._context.moveTo(x, y);\n          break;\n        }\n      case 1:\n        this._point = 2;\n      // falls through\n      default:\n        {\n          if (this._x) this._context.bezierCurveTo(this._x0 = (this._x0 + x) / 2, this._y0, this._x0, y, x, y);else this._context.bezierCurveTo(this._x0, this._y0 = (this._y0 + y) / 2, x, this._y0, x, y);\n          break;\n        }\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\nclass BumpRadial {\n  constructor(context) {\n    this._context = context;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {}\n  point(x, y) {\n    x = +x, y = +y;\n    if (this._point === 0) {\n      this._point = 1;\n    } else {\n      const p0 = pointRadial(this._x0, this._y0);\n      const p1 = pointRadial(this._x0, this._y0 = (this._y0 + y) / 2);\n      const p2 = pointRadial(x, this._y0);\n      const p3 = pointRadial(x, y);\n      this._context.moveTo(...p0);\n      this._context.bezierCurveTo(...p1, ...p2, ...p3);\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\nexport function bumpX(context) {\n  return new Bump(context, true);\n}\nexport function bumpY(context) {\n  return new Bump(context, false);\n}\nexport function bumpRadial(context) {\n  return new BumpRadial(context);\n}", "map": {"version": 3, "names": ["pointRadial", "Bump", "constructor", "context", "x", "_context", "_x", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_point", "lineEnd", "closePath", "point", "y", "lineTo", "moveTo", "bezierCurveTo", "_x0", "_y0", "BumpRadial", "p0", "p1", "p2", "p3", "bumpX", "bumpY", "bumpRadial"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-shape/src/curve/bump.js"], "sourcesContent": ["import pointRadial from \"../pointRadial.js\";\n\nclass Bump {\n  constructor(context, x) {\n    this._context = context;\n    this._x = x;\n  }\n  areaStart() {\n    this._line = 0;\n  }\n  areaEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  }\n  point(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: {\n        this._point = 1;\n        if (this._line) this._context.lineTo(x, y);\n        else this._context.moveTo(x, y);\n        break;\n      }\n      case 1: this._point = 2; // falls through\n      default: {\n        if (this._x) this._context.bezierCurveTo(this._x0 = (this._x0 + x) / 2, this._y0, this._x0, y, x, y);\n        else this._context.bezierCurveTo(this._x0, this._y0 = (this._y0 + y) / 2, x, this._y0, x, y);\n        break;\n      }\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\n\nclass BumpRadial {\n  constructor(context) {\n    this._context = context;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {}\n  point(x, y) {\n    x = +x, y = +y;\n    if (this._point === 0) {\n      this._point = 1;\n    } else {\n      const p0 = pointRadial(this._x0, this._y0);\n      const p1 = pointRadial(this._x0, this._y0 = (this._y0 + y) / 2);\n      const p2 = pointRadial(x, this._y0);\n      const p3 = pointRadial(x, y);\n      this._context.moveTo(...p0);\n      this._context.bezierCurveTo(...p1, ...p2, ...p3);\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\n\nexport function bumpX(context) {\n  return new Bump(context, true);\n}\n\nexport function bumpY(context) {\n  return new Bump(context, false);\n}\n\nexport function bumpRadial(context) {\n  return new BumpRadial(context);\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAE3C,MAAMC,IAAI,CAAC;EACTC,WAAWA,CAACC,OAAO,EAAEC,CAAC,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAGF,OAAO;IACvB,IAAI,CAACG,EAAE,GAAGF,CAAC;EACb;EACAG,SAASA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB;EACAC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB;EACAC,SAASA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB;EACAC,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAACL,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACI,MAAM,KAAK,CAAE,EAAE,IAAI,CAACP,QAAQ,CAACS,SAAS,CAAC,CAAC;IACpF,IAAI,CAACN,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B;EACAO,KAAKA,CAACX,CAAC,EAAEY,CAAC,EAAE;IACVZ,CAAC,GAAG,CAACA,CAAC,EAAEY,CAAC,GAAG,CAACA,CAAC;IACd,QAAQ,IAAI,CAACJ,MAAM;MACjB,KAAK,CAAC;QAAE;UACN,IAAI,CAACA,MAAM,GAAG,CAAC;UACf,IAAI,IAAI,CAACJ,KAAK,EAAE,IAAI,CAACH,QAAQ,CAACY,MAAM,CAACb,CAAC,EAAEY,CAAC,CAAC,CAAC,KACtC,IAAI,CAACX,QAAQ,CAACa,MAAM,CAACd,CAAC,EAAEY,CAAC,CAAC;UAC/B;QACF;MACA,KAAK,CAAC;QAAE,IAAI,CAACJ,MAAM,GAAG,CAAC;MAAE;MACzB;QAAS;UACP,IAAI,IAAI,CAACN,EAAE,EAAE,IAAI,CAACD,QAAQ,CAACc,aAAa,CAAC,IAAI,CAACC,GAAG,GAAG,CAAC,IAAI,CAACA,GAAG,GAAGhB,CAAC,IAAI,CAAC,EAAE,IAAI,CAACiB,GAAG,EAAE,IAAI,CAACD,GAAG,EAAEJ,CAAC,EAAEZ,CAAC,EAAEY,CAAC,CAAC,CAAC,KAChG,IAAI,CAACX,QAAQ,CAACc,aAAa,CAAC,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,GAAG,CAAC,IAAI,CAACA,GAAG,GAAGL,CAAC,IAAI,CAAC,EAAEZ,CAAC,EAAE,IAAI,CAACiB,GAAG,EAAEjB,CAAC,EAAEY,CAAC,CAAC;UAC5F;QACF;IACF;IACA,IAAI,CAACI,GAAG,GAAGhB,CAAC,EAAE,IAAI,CAACiB,GAAG,GAAGL,CAAC;EAC5B;AACF;AAEA,MAAMM,UAAU,CAAC;EACfpB,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACE,QAAQ,GAAGF,OAAO;EACzB;EACAQ,SAASA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB;EACAC,OAAOA,CAAA,EAAG,CAAC;EACXE,KAAKA,CAACX,CAAC,EAAEY,CAAC,EAAE;IACVZ,CAAC,GAAG,CAACA,CAAC,EAAEY,CAAC,GAAG,CAACA,CAAC;IACd,IAAI,IAAI,CAACJ,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,CAACA,MAAM,GAAG,CAAC;IACjB,CAAC,MAAM;MACL,MAAMW,EAAE,GAAGvB,WAAW,CAAC,IAAI,CAACoB,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;MAC1C,MAAMG,EAAE,GAAGxB,WAAW,CAAC,IAAI,CAACoB,GAAG,EAAE,IAAI,CAACC,GAAG,GAAG,CAAC,IAAI,CAACA,GAAG,GAAGL,CAAC,IAAI,CAAC,CAAC;MAC/D,MAAMS,EAAE,GAAGzB,WAAW,CAACI,CAAC,EAAE,IAAI,CAACiB,GAAG,CAAC;MACnC,MAAMK,EAAE,GAAG1B,WAAW,CAACI,CAAC,EAAEY,CAAC,CAAC;MAC5B,IAAI,CAACX,QAAQ,CAACa,MAAM,CAAC,GAAGK,EAAE,CAAC;MAC3B,IAAI,CAAClB,QAAQ,CAACc,aAAa,CAAC,GAAGK,EAAE,EAAE,GAAGC,EAAE,EAAE,GAAGC,EAAE,CAAC;IAClD;IACA,IAAI,CAACN,GAAG,GAAGhB,CAAC,EAAE,IAAI,CAACiB,GAAG,GAAGL,CAAC;EAC5B;AACF;AAEA,OAAO,SAASW,KAAKA,CAACxB,OAAO,EAAE;EAC7B,OAAO,IAAIF,IAAI,CAACE,OAAO,EAAE,IAAI,CAAC;AAChC;AAEA,OAAO,SAASyB,KAAKA,CAACzB,OAAO,EAAE;EAC7B,OAAO,IAAIF,IAAI,CAACE,OAAO,EAAE,KAAK,CAAC;AACjC;AAEA,OAAO,SAAS0B,UAAUA,CAAC1B,OAAO,EAAE;EAClC,OAAO,IAAImB,UAAU,CAACnB,OAAO,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}