{"ast": null, "code": "var _excluded = [\"children\", \"begin\", \"duration\", \"attributeName\", \"easing\", \"isActive\", \"from\", \"to\", \"canBegin\", \"onAnimationEnd\", \"shouldReAnimate\", \"onAnimationReStart\", \"animationManager\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport * as React from 'react';\nimport { PureComponent, cloneElement, Children, createContext, useContext } from 'react';\nimport isEqual from 'es-toolkit/compat/isEqual';\nimport { createAnimateManager } from './AnimationManager';\nimport { configEasing } from './easing';\nimport configUpdate from './configUpdate';\nimport { getTransitionVal } from './util';\nimport { RequestAnimationFrameTimeoutController } from './timeoutController';\nfunction createDefaultAnimationManager() {\n  return createAnimateManager(new RequestAnimationFrameTimeoutController());\n}\nclass AnimateImpl extends PureComponent {\n  constructor(props, context) {\n    super(props, context);\n    _defineProperty(this, \"mounted\", false);\n    _defineProperty(this, \"manager\", null);\n    _defineProperty(this, \"stopJSAnimation\", null);\n    _defineProperty(this, \"unSubscribe\", null);\n    var {\n      isActive,\n      attributeName,\n      from,\n      to,\n      children,\n      duration,\n      animationManager\n    } = this.props;\n    this.manager = animationManager;\n    this.handleStyleChange = this.handleStyleChange.bind(this);\n    this.changeStyle = this.changeStyle.bind(this);\n    if (!isActive || duration <= 0) {\n      this.state = {\n        style: {}\n      };\n\n      // if children is a function and animation is not active, set style to 'to'\n      if (typeof children === 'function') {\n        this.state = {\n          style: to\n        };\n      }\n      return;\n    }\n    if (from) {\n      if (typeof children === 'function') {\n        this.state = {\n          style: from\n        };\n        return;\n      }\n      this.state = {\n        style: attributeName ? {\n          [attributeName]: from\n        } : from\n      };\n    } else {\n      this.state = {\n        style: {}\n      };\n    }\n  }\n  componentDidMount() {\n    var {\n      isActive,\n      canBegin\n    } = this.props;\n    this.mounted = true;\n    if (!isActive || !canBegin) {\n      return;\n    }\n    this.runAnimation(this.props);\n  }\n  componentDidUpdate(prevProps) {\n    var {\n      isActive,\n      canBegin,\n      attributeName,\n      shouldReAnimate,\n      to,\n      from: currentFrom\n    } = this.props;\n    var {\n      style\n    } = this.state;\n    if (!canBegin) {\n      return;\n    }\n    if (!isActive) {\n      var newState = {\n        style: attributeName ? {\n          [attributeName]: to\n        } : to\n      };\n      if (this.state && style) {\n        if (attributeName && style[attributeName] !== to || !attributeName && style !== to) {\n          this.setState(newState);\n        }\n      }\n      return;\n    }\n    if (isEqual(prevProps.to, to) && prevProps.canBegin && prevProps.isActive) {\n      return;\n    }\n    var isTriggered = !prevProps.canBegin || !prevProps.isActive;\n    this.manager.stop();\n    if (this.stopJSAnimation) {\n      this.stopJSAnimation();\n    }\n    var from = isTriggered || shouldReAnimate ? currentFrom : prevProps.to;\n    if (this.state && style) {\n      var _newState = {\n        style: attributeName ? {\n          [attributeName]: from\n        } : from\n      };\n      if (attributeName && style[attributeName] !== from || !attributeName && style !== from) {\n        this.setState(_newState);\n      }\n    }\n    this.runAnimation(_objectSpread(_objectSpread({}, this.props), {}, {\n      from,\n      begin: 0\n    }));\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n    var {\n      onAnimationEnd\n    } = this.props;\n    if (this.unSubscribe) {\n      this.unSubscribe();\n    }\n    this.manager.stop();\n    if (this.stopJSAnimation) {\n      this.stopJSAnimation();\n    }\n    if (onAnimationEnd) {\n      onAnimationEnd();\n    }\n  }\n  handleStyleChange(style) {\n    this.changeStyle(style);\n  }\n  changeStyle(style) {\n    if (this.mounted) {\n      this.setState({\n        style\n      });\n    }\n  }\n  runJSAnimation(props) {\n    var {\n      from,\n      to,\n      duration,\n      easing,\n      begin,\n      onAnimationEnd,\n      onAnimationStart\n    } = props;\n    var startAnimation = configUpdate(from, to, configEasing(easing), duration, this.changeStyle, this.manager.getTimeoutController());\n    var finalStartAnimation = () => {\n      this.stopJSAnimation = startAnimation();\n    };\n    this.manager.start([onAnimationStart, begin, finalStartAnimation, duration, onAnimationEnd]);\n  }\n  runAnimation(props) {\n    var {\n      begin,\n      duration,\n      attributeName,\n      to: propsTo,\n      easing,\n      onAnimationStart,\n      onAnimationEnd,\n      children\n    } = props;\n    this.unSubscribe = this.manager.subscribe(this.handleStyleChange);\n    if (typeof easing === 'function' || typeof children === 'function' || easing === 'spring') {\n      this.runJSAnimation(props);\n      return;\n    }\n    var to = attributeName ? {\n      [attributeName]: propsTo\n    } : propsTo;\n    var transition = getTransitionVal(Object.keys(to), duration, easing);\n    this.manager.start([onAnimationStart, begin, _objectSpread(_objectSpread({}, to), {}, {\n      transition\n    }), duration, onAnimationEnd]);\n  }\n  render() {\n    var _this$props = this.props,\n      {\n        children,\n        begin,\n        duration,\n        attributeName,\n        easing,\n        isActive,\n        from,\n        to,\n        canBegin,\n        onAnimationEnd,\n        shouldReAnimate,\n        onAnimationReStart,\n        animationManager\n      } = _this$props,\n      others = _objectWithoutProperties(_this$props, _excluded);\n    var count = Children.count(children);\n    var stateStyle = this.state.style;\n    if (typeof children === 'function') {\n      return children(stateStyle);\n    }\n    if (!isActive || count === 0 || duration <= 0) {\n      return children;\n    }\n    var cloneContainer = container => {\n      var {\n        style = {},\n        className\n      } = container.props;\n      var res = /*#__PURE__*/cloneElement(container, _objectSpread(_objectSpread({}, others), {}, {\n        style: _objectSpread(_objectSpread({}, style), stateStyle),\n        className\n      }));\n      return res;\n    };\n    if (count === 1) {\n      // @ts-expect-error TODO - fix the type error\n      return cloneContainer(Children.only(children));\n    }\n\n    // @ts-expect-error TODO - fix the type error\n    return /*#__PURE__*/React.createElement(\"div\", null, Children.map(children, child => cloneContainer(child)));\n  }\n}\n_defineProperty(AnimateImpl, \"displayName\", 'Animate');\n_defineProperty(AnimateImpl, \"defaultProps\", {\n  begin: 0,\n  duration: 1000,\n  attributeName: '',\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  onAnimationEnd: () => {},\n  onAnimationStart: () => {}\n});\nexport var AnimationManagerContext = /*#__PURE__*/createContext(null);\nexport function Animate(props) {\n  var _ref, _props$animationManag;\n  var contextAnimationManager = useContext(AnimationManagerContext);\n  return /*#__PURE__*/React.createElement(AnimateImpl, _extends({}, props, {\n    animationManager: (_ref = (_props$animationManag = props.animationManager) !== null && _props$animationManag !== void 0 ? _props$animationManag : contextAnimationManager) !== null && _ref !== void 0 ? _ref : createDefaultAnimationManager()\n  }));\n}", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "o", "i", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "PureComponent", "cloneElement", "Children", "createContext", "useContext", "isEqual", "createAnimateManager", "configEasing", "configUpdate", "getTransitionVal", "RequestAnimationFrameTimeoutController", "createDefaultAnimationManager", "AnimateImpl", "constructor", "props", "context", "isActive", "attributeName", "from", "to", "children", "duration", "animationManager", "manager", "handleStyleChange", "changeStyle", "state", "style", "componentDidMount", "canBegin", "mounted", "runAnimation", "componentDidUpdate", "prevProps", "shouldReAnimate", "currentFrom", "newState", "setState", "isTriggered", "stop", "stopJSAnimation", "_newState", "begin", "componentWillUnmount", "onAnimationEnd", "unSubscribe", "runJSAnimation", "easing", "onAnimationStart", "startAnimation", "getTimeoutController", "finalStartAnimation", "start", "propsTo", "subscribe", "transition", "render", "_this$props", "onAnimationReStart", "others", "count", "stateStyle", "cloneContainer", "container", "className", "res", "only", "createElement", "map", "child", "AnimationManagerContext", "Animate", "_ref", "_props$animationManag", "contextAnimationManager"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/animation/Animate.js"], "sourcesContent": ["var _excluded = [\"children\", \"begin\", \"duration\", \"attributeName\", \"easing\", \"isActive\", \"from\", \"to\", \"canBegin\", \"onAnimationEnd\", \"shouldReAnimate\", \"onAnimationReStart\", \"animationManager\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { PureComponent, cloneElement, Children, createContext, useContext } from 'react';\nimport isEqual from 'es-toolkit/compat/isEqual';\nimport { createAnimateManager } from './AnimationManager';\nimport { configEasing } from './easing';\nimport configUpdate from './configUpdate';\nimport { getTransitionVal } from './util';\nimport { RequestAnimationFrameTimeoutController } from './timeoutController';\nfunction createDefaultAnimationManager() {\n  return createAnimateManager(new RequestAnimationFrameTimeoutController());\n}\nclass AnimateImpl extends PureComponent {\n  constructor(props, context) {\n    super(props, context);\n    _defineProperty(this, \"mounted\", false);\n    _defineProperty(this, \"manager\", null);\n    _defineProperty(this, \"stopJSAnimation\", null);\n    _defineProperty(this, \"unSubscribe\", null);\n    var {\n      isActive,\n      attributeName,\n      from,\n      to,\n      children,\n      duration,\n      animationManager\n    } = this.props;\n    this.manager = animationManager;\n    this.handleStyleChange = this.handleStyleChange.bind(this);\n    this.changeStyle = this.changeStyle.bind(this);\n    if (!isActive || duration <= 0) {\n      this.state = {\n        style: {}\n      };\n\n      // if children is a function and animation is not active, set style to 'to'\n      if (typeof children === 'function') {\n        this.state = {\n          style: to\n        };\n      }\n      return;\n    }\n    if (from) {\n      if (typeof children === 'function') {\n        this.state = {\n          style: from\n        };\n        return;\n      }\n      this.state = {\n        style: attributeName ? {\n          [attributeName]: from\n        } : from\n      };\n    } else {\n      this.state = {\n        style: {}\n      };\n    }\n  }\n  componentDidMount() {\n    var {\n      isActive,\n      canBegin\n    } = this.props;\n    this.mounted = true;\n    if (!isActive || !canBegin) {\n      return;\n    }\n    this.runAnimation(this.props);\n  }\n  componentDidUpdate(prevProps) {\n    var {\n      isActive,\n      canBegin,\n      attributeName,\n      shouldReAnimate,\n      to,\n      from: currentFrom\n    } = this.props;\n    var {\n      style\n    } = this.state;\n    if (!canBegin) {\n      return;\n    }\n    if (!isActive) {\n      var newState = {\n        style: attributeName ? {\n          [attributeName]: to\n        } : to\n      };\n      if (this.state && style) {\n        if (attributeName && style[attributeName] !== to || !attributeName && style !== to) {\n          this.setState(newState);\n        }\n      }\n      return;\n    }\n    if (isEqual(prevProps.to, to) && prevProps.canBegin && prevProps.isActive) {\n      return;\n    }\n    var isTriggered = !prevProps.canBegin || !prevProps.isActive;\n    this.manager.stop();\n    if (this.stopJSAnimation) {\n      this.stopJSAnimation();\n    }\n    var from = isTriggered || shouldReAnimate ? currentFrom : prevProps.to;\n    if (this.state && style) {\n      var _newState = {\n        style: attributeName ? {\n          [attributeName]: from\n        } : from\n      };\n      if (attributeName && style[attributeName] !== from || !attributeName && style !== from) {\n        this.setState(_newState);\n      }\n    }\n    this.runAnimation(_objectSpread(_objectSpread({}, this.props), {}, {\n      from,\n      begin: 0\n    }));\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n    var {\n      onAnimationEnd\n    } = this.props;\n    if (this.unSubscribe) {\n      this.unSubscribe();\n    }\n    this.manager.stop();\n    if (this.stopJSAnimation) {\n      this.stopJSAnimation();\n    }\n    if (onAnimationEnd) {\n      onAnimationEnd();\n    }\n  }\n  handleStyleChange(style) {\n    this.changeStyle(style);\n  }\n  changeStyle(style) {\n    if (this.mounted) {\n      this.setState({\n        style\n      });\n    }\n  }\n  runJSAnimation(props) {\n    var {\n      from,\n      to,\n      duration,\n      easing,\n      begin,\n      onAnimationEnd,\n      onAnimationStart\n    } = props;\n    var startAnimation = configUpdate(from, to, configEasing(easing), duration, this.changeStyle, this.manager.getTimeoutController());\n    var finalStartAnimation = () => {\n      this.stopJSAnimation = startAnimation();\n    };\n    this.manager.start([onAnimationStart, begin, finalStartAnimation, duration, onAnimationEnd]);\n  }\n  runAnimation(props) {\n    var {\n      begin,\n      duration,\n      attributeName,\n      to: propsTo,\n      easing,\n      onAnimationStart,\n      onAnimationEnd,\n      children\n    } = props;\n    this.unSubscribe = this.manager.subscribe(this.handleStyleChange);\n    if (typeof easing === 'function' || typeof children === 'function' || easing === 'spring') {\n      this.runJSAnimation(props);\n      return;\n    }\n    var to = attributeName ? {\n      [attributeName]: propsTo\n    } : propsTo;\n    var transition = getTransitionVal(Object.keys(to), duration, easing);\n    this.manager.start([onAnimationStart, begin, _objectSpread(_objectSpread({}, to), {}, {\n      transition\n    }), duration, onAnimationEnd]);\n  }\n  render() {\n    var _this$props = this.props,\n      {\n        children,\n        begin,\n        duration,\n        attributeName,\n        easing,\n        isActive,\n        from,\n        to,\n        canBegin,\n        onAnimationEnd,\n        shouldReAnimate,\n        onAnimationReStart,\n        animationManager\n      } = _this$props,\n      others = _objectWithoutProperties(_this$props, _excluded);\n    var count = Children.count(children);\n    var stateStyle = this.state.style;\n    if (typeof children === 'function') {\n      return children(stateStyle);\n    }\n    if (!isActive || count === 0 || duration <= 0) {\n      return children;\n    }\n    var cloneContainer = container => {\n      var {\n        style = {},\n        className\n      } = container.props;\n      var res = /*#__PURE__*/cloneElement(container, _objectSpread(_objectSpread({}, others), {}, {\n        style: _objectSpread(_objectSpread({}, style), stateStyle),\n        className\n      }));\n      return res;\n    };\n    if (count === 1) {\n      // @ts-expect-error TODO - fix the type error\n      return cloneContainer(Children.only(children));\n    }\n\n    // @ts-expect-error TODO - fix the type error\n    return /*#__PURE__*/React.createElement(\"div\", null, Children.map(children, child => cloneContainer(child)));\n  }\n}\n_defineProperty(AnimateImpl, \"displayName\", 'Animate');\n_defineProperty(AnimateImpl, \"defaultProps\", {\n  begin: 0,\n  duration: 1000,\n  attributeName: '',\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  onAnimationEnd: () => {},\n  onAnimationStart: () => {}\n});\nexport var AnimationManagerContext = /*#__PURE__*/createContext(null);\nexport function Animate(props) {\n  var _ref, _props$animationManag;\n  var contextAnimationManager = useContext(AnimationManagerContext);\n  return /*#__PURE__*/React.createElement(AnimateImpl, _extends({}, props, {\n    animationManager: (_ref = (_props$animationManag = props.animationManager) !== null && _props$animationManag !== void 0 ? _props$animationManag : contextAnimationManager) !== null && _ref !== void 0 ? _ref : createDefaultAnimationManager()\n  }));\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC;AACjM,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,wBAAwBA,CAACR,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIS,CAAC;IAAEL,CAAC;IAAEM,CAAC,GAAGC,6BAA6B,CAACX,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGH,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEK,CAAC,GAAGV,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACU,OAAO,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,CAACK,oBAAoB,CAACR,IAAI,CAACN,CAAC,EAAES,CAAC,CAAC,KAAKC,CAAC,CAACD,CAAC,CAAC,GAAGT,CAAC,CAACS,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACP,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACa,OAAO,CAACd,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,SAASY,OAAOA,CAACf,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACoB,IAAI,CAAChB,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIH,CAAC,GAAGb,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAEI,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACQ,MAAM,CAAC,UAAUb,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACsB,wBAAwB,CAAClB,CAAC,EAAEI,CAAC,CAAC,CAACe,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEhB,CAAC,CAACiB,IAAI,CAACb,KAAK,CAACJ,CAAC,EAAEM,CAAC,CAAC;EAAE;EAAE,OAAON,CAAC;AAAE;AAC9P,SAASkB,aAAaA,CAACrB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGW,OAAO,CAACnB,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACmB,OAAO,CAAC,UAAUlB,CAAC,EAAE;MAAEmB,eAAe,CAACvB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC4B,yBAAyB,GAAG5B,MAAM,CAAC6B,gBAAgB,CAACzB,CAAC,EAAEJ,MAAM,CAAC4B,yBAAyB,CAACrB,CAAC,CAAC,CAAC,GAAGY,OAAO,CAACnB,MAAM,CAACO,CAAC,CAAC,CAAC,CAACmB,OAAO,CAAC,UAAUlB,CAAC,EAAE;MAAER,MAAM,CAAC8B,cAAc,CAAC1B,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACsB,wBAAwB,CAACf,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASuB,eAAeA,CAACvB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGuB,cAAc,CAACvB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAAC8B,cAAc,CAAC1B,CAAC,EAAEI,CAAC,EAAE;IAAEwB,KAAK,EAAEzB,CAAC;IAAEgB,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG9B,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAAS2B,cAAcA,CAACxB,CAAC,EAAE;EAAE,IAAIO,CAAC,GAAGqB,YAAY,CAAC5B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOO,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASqB,YAAYA,CAAC5B,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAAC6B,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKjC,CAAC,EAAE;IAAE,IAAIU,CAAC,GAAGV,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOM,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIwB,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK9B,CAAC,GAAG+B,MAAM,GAAGC,MAAM,EAAEjC,CAAC,CAAC;AAAE;AACvT,OAAO,KAAKkC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACxF,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,gBAAgB,QAAQ,QAAQ;AACzC,SAASC,sCAAsC,QAAQ,qBAAqB;AAC5E,SAASC,6BAA6BA,CAAA,EAAG;EACvC,OAAOL,oBAAoB,CAAC,IAAII,sCAAsC,CAAC,CAAC,CAAC;AAC3E;AACA,MAAME,WAAW,SAASZ,aAAa,CAAC;EACtCa,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB9B,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC;IACvCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC;IACtCA,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,CAAC;IAC9CA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC;IAC1C,IAAI;MACF+B,QAAQ;MACRC,aAAa;MACbC,IAAI;MACJC,EAAE;MACFC,QAAQ;MACRC,QAAQ;MACRC;IACF,CAAC,GAAG,IAAI,CAACR,KAAK;IACd,IAAI,CAACS,OAAO,GAAGD,gBAAgB;IAC/B,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAChE,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACiE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACjE,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACwD,QAAQ,IAAIK,QAAQ,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACK,KAAK,GAAG;QACXC,KAAK,EAAE,CAAC;MACV,CAAC;;MAED;MACA,IAAI,OAAOP,QAAQ,KAAK,UAAU,EAAE;QAClC,IAAI,CAACM,KAAK,GAAG;UACXC,KAAK,EAAER;QACT,CAAC;MACH;MACA;IACF;IACA,IAAID,IAAI,EAAE;MACR,IAAI,OAAOE,QAAQ,KAAK,UAAU,EAAE;QAClC,IAAI,CAACM,KAAK,GAAG;UACXC,KAAK,EAAET;QACT,CAAC;QACD;MACF;MACA,IAAI,CAACQ,KAAK,GAAG;QACXC,KAAK,EAAEV,aAAa,GAAG;UACrB,CAACA,aAAa,GAAGC;QACnB,CAAC,GAAGA;MACN,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACQ,KAAK,GAAG;QACXC,KAAK,EAAE,CAAC;MACV,CAAC;IACH;EACF;EACAC,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACFZ,QAAQ;MACRa;IACF,CAAC,GAAG,IAAI,CAACf,KAAK;IACd,IAAI,CAACgB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACd,QAAQ,IAAI,CAACa,QAAQ,EAAE;MAC1B;IACF;IACA,IAAI,CAACE,YAAY,CAAC,IAAI,CAACjB,KAAK,CAAC;EAC/B;EACAkB,kBAAkBA,CAACC,SAAS,EAAE;IAC5B,IAAI;MACFjB,QAAQ;MACRa,QAAQ;MACRZ,aAAa;MACbiB,eAAe;MACff,EAAE;MACFD,IAAI,EAAEiB;IACR,CAAC,GAAG,IAAI,CAACrB,KAAK;IACd,IAAI;MACFa;IACF,CAAC,GAAG,IAAI,CAACD,KAAK;IACd,IAAI,CAACG,QAAQ,EAAE;MACb;IACF;IACA,IAAI,CAACb,QAAQ,EAAE;MACb,IAAIoB,QAAQ,GAAG;QACbT,KAAK,EAAEV,aAAa,GAAG;UACrB,CAACA,aAAa,GAAGE;QACnB,CAAC,GAAGA;MACN,CAAC;MACD,IAAI,IAAI,CAACO,KAAK,IAAIC,KAAK,EAAE;QACvB,IAAIV,aAAa,IAAIU,KAAK,CAACV,aAAa,CAAC,KAAKE,EAAE,IAAI,CAACF,aAAa,IAAIU,KAAK,KAAKR,EAAE,EAAE;UAClF,IAAI,CAACkB,QAAQ,CAACD,QAAQ,CAAC;QACzB;MACF;MACA;IACF;IACA,IAAI/B,OAAO,CAAC4B,SAAS,CAACd,EAAE,EAAEA,EAAE,CAAC,IAAIc,SAAS,CAACJ,QAAQ,IAAII,SAAS,CAACjB,QAAQ,EAAE;MACzE;IACF;IACA,IAAIsB,WAAW,GAAG,CAACL,SAAS,CAACJ,QAAQ,IAAI,CAACI,SAAS,CAACjB,QAAQ;IAC5D,IAAI,CAACO,OAAO,CAACgB,IAAI,CAAC,CAAC;IACnB,IAAI,IAAI,CAACC,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAAC,CAAC;IACxB;IACA,IAAItB,IAAI,GAAGoB,WAAW,IAAIJ,eAAe,GAAGC,WAAW,GAAGF,SAAS,CAACd,EAAE;IACtE,IAAI,IAAI,CAACO,KAAK,IAAIC,KAAK,EAAE;MACvB,IAAIc,SAAS,GAAG;QACdd,KAAK,EAAEV,aAAa,GAAG;UACrB,CAACA,aAAa,GAAGC;QACnB,CAAC,GAAGA;MACN,CAAC;MACD,IAAID,aAAa,IAAIU,KAAK,CAACV,aAAa,CAAC,KAAKC,IAAI,IAAI,CAACD,aAAa,IAAIU,KAAK,KAAKT,IAAI,EAAE;QACtF,IAAI,CAACmB,QAAQ,CAACI,SAAS,CAAC;MAC1B;IACF;IACA,IAAI,CAACV,YAAY,CAAChD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC+B,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjEI,IAAI;MACJwB,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;EACL;EACAC,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAACb,OAAO,GAAG,KAAK;IACpB,IAAI;MACFc;IACF,CAAC,GAAG,IAAI,CAAC9B,KAAK;IACd,IAAI,IAAI,CAAC+B,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC,CAAC;IACpB;IACA,IAAI,CAACtB,OAAO,CAACgB,IAAI,CAAC,CAAC;IACnB,IAAI,IAAI,CAACC,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAAC,CAAC;IACxB;IACA,IAAII,cAAc,EAAE;MAClBA,cAAc,CAAC,CAAC;IAClB;EACF;EACApB,iBAAiBA,CAACG,KAAK,EAAE;IACvB,IAAI,CAACF,WAAW,CAACE,KAAK,CAAC;EACzB;EACAF,WAAWA,CAACE,KAAK,EAAE;IACjB,IAAI,IAAI,CAACG,OAAO,EAAE;MAChB,IAAI,CAACO,QAAQ,CAAC;QACZV;MACF,CAAC,CAAC;IACJ;EACF;EACAmB,cAAcA,CAAChC,KAAK,EAAE;IACpB,IAAI;MACFI,IAAI;MACJC,EAAE;MACFE,QAAQ;MACR0B,MAAM;MACNL,KAAK;MACLE,cAAc;MACdI;IACF,CAAC,GAAGlC,KAAK;IACT,IAAImC,cAAc,GAAGzC,YAAY,CAACU,IAAI,EAAEC,EAAE,EAAEZ,YAAY,CAACwC,MAAM,CAAC,EAAE1B,QAAQ,EAAE,IAAI,CAACI,WAAW,EAAE,IAAI,CAACF,OAAO,CAAC2B,oBAAoB,CAAC,CAAC,CAAC;IAClI,IAAIC,mBAAmB,GAAGA,CAAA,KAAM;MAC9B,IAAI,CAACX,eAAe,GAAGS,cAAc,CAAC,CAAC;IACzC,CAAC;IACD,IAAI,CAAC1B,OAAO,CAAC6B,KAAK,CAAC,CAACJ,gBAAgB,EAAEN,KAAK,EAAES,mBAAmB,EAAE9B,QAAQ,EAAEuB,cAAc,CAAC,CAAC;EAC9F;EACAb,YAAYA,CAACjB,KAAK,EAAE;IAClB,IAAI;MACF4B,KAAK;MACLrB,QAAQ;MACRJ,aAAa;MACbE,EAAE,EAAEkC,OAAO;MACXN,MAAM;MACNC,gBAAgB;MAChBJ,cAAc;MACdxB;IACF,CAAC,GAAGN,KAAK;IACT,IAAI,CAAC+B,WAAW,GAAG,IAAI,CAACtB,OAAO,CAAC+B,SAAS,CAAC,IAAI,CAAC9B,iBAAiB,CAAC;IACjE,IAAI,OAAOuB,MAAM,KAAK,UAAU,IAAI,OAAO3B,QAAQ,KAAK,UAAU,IAAI2B,MAAM,KAAK,QAAQ,EAAE;MACzF,IAAI,CAACD,cAAc,CAAChC,KAAK,CAAC;MAC1B;IACF;IACA,IAAIK,EAAE,GAAGF,aAAa,GAAG;MACvB,CAACA,aAAa,GAAGoC;IACnB,CAAC,GAAGA,OAAO;IACX,IAAIE,UAAU,GAAG9C,gBAAgB,CAACnD,MAAM,CAACoB,IAAI,CAACyC,EAAE,CAAC,EAAEE,QAAQ,EAAE0B,MAAM,CAAC;IACpE,IAAI,CAACxB,OAAO,CAAC6B,KAAK,CAAC,CAACJ,gBAAgB,EAAEN,KAAK,EAAE3D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;MACpFoC;IACF,CAAC,CAAC,EAAElC,QAAQ,EAAEuB,cAAc,CAAC,CAAC;EAChC;EACAY,MAAMA,CAAA,EAAG;IACP,IAAIC,WAAW,GAAG,IAAI,CAAC3C,KAAK;MAC1B;QACEM,QAAQ;QACRsB,KAAK;QACLrB,QAAQ;QACRJ,aAAa;QACb8B,MAAM;QACN/B,QAAQ;QACRE,IAAI;QACJC,EAAE;QACFU,QAAQ;QACRe,cAAc;QACdV,eAAe;QACfwB,kBAAkB;QAClBpC;MACF,CAAC,GAAGmC,WAAW;MACfE,MAAM,GAAGzF,wBAAwB,CAACuF,WAAW,EAAErG,SAAS,CAAC;IAC3D,IAAIwG,KAAK,GAAG1D,QAAQ,CAAC0D,KAAK,CAACxC,QAAQ,CAAC;IACpC,IAAIyC,UAAU,GAAG,IAAI,CAACnC,KAAK,CAACC,KAAK;IACjC,IAAI,OAAOP,QAAQ,KAAK,UAAU,EAAE;MAClC,OAAOA,QAAQ,CAACyC,UAAU,CAAC;IAC7B;IACA,IAAI,CAAC7C,QAAQ,IAAI4C,KAAK,KAAK,CAAC,IAAIvC,QAAQ,IAAI,CAAC,EAAE;MAC7C,OAAOD,QAAQ;IACjB;IACA,IAAI0C,cAAc,GAAGC,SAAS,IAAI;MAChC,IAAI;QACFpC,KAAK,GAAG,CAAC,CAAC;QACVqC;MACF,CAAC,GAAGD,SAAS,CAACjD,KAAK;MACnB,IAAImD,GAAG,GAAG,aAAahE,YAAY,CAAC8D,SAAS,EAAEhF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4E,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1FhC,KAAK,EAAE5C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAAC,EAAEkC,UAAU,CAAC;QAC1DG;MACF,CAAC,CAAC,CAAC;MACH,OAAOC,GAAG;IACZ,CAAC;IACD,IAAIL,KAAK,KAAK,CAAC,EAAE;MACf;MACA,OAAOE,cAAc,CAAC5D,QAAQ,CAACgE,IAAI,CAAC9C,QAAQ,CAAC,CAAC;IAChD;;IAEA;IACA,OAAO,aAAarB,KAAK,CAACoE,aAAa,CAAC,KAAK,EAAE,IAAI,EAAEjE,QAAQ,CAACkE,GAAG,CAAChD,QAAQ,EAAEiD,KAAK,IAAIP,cAAc,CAACO,KAAK,CAAC,CAAC,CAAC;EAC9G;AACF;AACApF,eAAe,CAAC2B,WAAW,EAAE,aAAa,EAAE,SAAS,CAAC;AACtD3B,eAAe,CAAC2B,WAAW,EAAE,cAAc,EAAE;EAC3C8B,KAAK,EAAE,CAAC;EACRrB,QAAQ,EAAE,IAAI;EACdJ,aAAa,EAAE,EAAE;EACjB8B,MAAM,EAAE,MAAM;EACd/B,QAAQ,EAAE,IAAI;EACda,QAAQ,EAAE,IAAI;EACde,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAC;EACxBI,gBAAgB,EAAEA,CAAA,KAAM,CAAC;AAC3B,CAAC,CAAC;AACF,OAAO,IAAIsB,uBAAuB,GAAG,aAAanE,aAAa,CAAC,IAAI,CAAC;AACrE,OAAO,SAASoE,OAAOA,CAACzD,KAAK,EAAE;EAC7B,IAAI0D,IAAI,EAAEC,qBAAqB;EAC/B,IAAIC,uBAAuB,GAAGtE,UAAU,CAACkE,uBAAuB,CAAC;EACjE,OAAO,aAAavE,KAAK,CAACoE,aAAa,CAACvD,WAAW,EAAEvD,QAAQ,CAAC,CAAC,CAAC,EAAEyD,KAAK,EAAE;IACvEQ,gBAAgB,EAAE,CAACkD,IAAI,GAAG,CAACC,qBAAqB,GAAG3D,KAAK,CAACQ,gBAAgB,MAAM,IAAI,IAAImD,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGC,uBAAuB,MAAM,IAAI,IAAIF,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG7D,6BAA6B,CAAC;EAChP,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}