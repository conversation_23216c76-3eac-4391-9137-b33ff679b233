{"ast": null, "code": "// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\nexport default function formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nexport function FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\nFormatSpecifier.prototype.toString = function () {\n  return this.fill + this.align + this.sign + this.symbol + (this.zero ? \"0\" : \"\") + (this.width === undefined ? \"\" : Math.max(1, this.width | 0)) + (this.comma ? \",\" : \"\") + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0)) + (this.trim ? \"~\" : \"\") + this.type;\n};", "map": {"version": 3, "names": ["re", "formatSpecifier", "specifier", "match", "exec", "Error", "FormatSpecifier", "fill", "align", "sign", "symbol", "zero", "width", "comma", "precision", "slice", "trim", "type", "prototype", "undefined", "toString", "Math", "max"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-format/src/formatSpecifier.js"], "sourcesContent": ["// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\n\nexport default function formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\n\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nexport function FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\n\nFormatSpecifier.prototype.toString = function() {\n  return this.fill\n      + this.align\n      + this.sign\n      + this.symbol\n      + (this.zero ? \"0\" : \"\")\n      + (this.width === undefined ? \"\" : Math.max(1, this.width | 0))\n      + (this.comma ? \",\" : \"\")\n      + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0))\n      + (this.trim ? \"~\" : \"\")\n      + this.type;\n};\n"], "mappings": "AAAA;AACA,IAAIA,EAAE,GAAG,0EAA0E;AAEnF,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAE;EACjD,IAAI,EAAEC,KAAK,GAAGH,EAAE,CAACI,IAAI,CAACF,SAAS,CAAC,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,kBAAkB,GAAGH,SAAS,CAAC;EAClF,IAAIC,KAAK;EACT,OAAO,IAAIG,eAAe,CAAC;IACzBC,IAAI,EAAEJ,KAAK,CAAC,CAAC,CAAC;IACdK,KAAK,EAAEL,KAAK,CAAC,CAAC,CAAC;IACfM,IAAI,EAAEN,KAAK,CAAC,CAAC,CAAC;IACdO,MAAM,EAAEP,KAAK,CAAC,CAAC,CAAC;IAChBQ,IAAI,EAAER,KAAK,CAAC,CAAC,CAAC;IACdS,KAAK,EAAET,KAAK,CAAC,CAAC,CAAC;IACfU,KAAK,EAAEV,KAAK,CAAC,CAAC,CAAC;IACfW,SAAS,EAAEX,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC,CAAC;IACxCC,IAAI,EAAEb,KAAK,CAAC,CAAC,CAAC;IACdc,IAAI,EAAEd,KAAK,CAAC,EAAE;EAChB,CAAC,CAAC;AACJ;AAEAF,eAAe,CAACiB,SAAS,GAAGZ,eAAe,CAACY,SAAS,CAAC,CAAC;;AAEvD,OAAO,SAASZ,eAAeA,CAACJ,SAAS,EAAE;EACzC,IAAI,CAACK,IAAI,GAAGL,SAAS,CAACK,IAAI,KAAKY,SAAS,GAAG,GAAG,GAAGjB,SAAS,CAACK,IAAI,GAAG,EAAE;EACpE,IAAI,CAACC,KAAK,GAAGN,SAAS,CAACM,KAAK,KAAKW,SAAS,GAAG,GAAG,GAAGjB,SAAS,CAACM,KAAK,GAAG,EAAE;EACvE,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACO,IAAI,KAAKU,SAAS,GAAG,GAAG,GAAGjB,SAAS,CAACO,IAAI,GAAG,EAAE;EACpE,IAAI,CAACC,MAAM,GAAGR,SAAS,CAACQ,MAAM,KAAKS,SAAS,GAAG,EAAE,GAAGjB,SAAS,CAACQ,MAAM,GAAG,EAAE;EACzE,IAAI,CAACC,IAAI,GAAG,CAAC,CAACT,SAAS,CAACS,IAAI;EAC5B,IAAI,CAACC,KAAK,GAAGV,SAAS,CAACU,KAAK,KAAKO,SAAS,GAAGA,SAAS,GAAG,CAACjB,SAAS,CAACU,KAAK;EACzE,IAAI,CAACC,KAAK,GAAG,CAAC,CAACX,SAAS,CAACW,KAAK;EAC9B,IAAI,CAACC,SAAS,GAAGZ,SAAS,CAACY,SAAS,KAAKK,SAAS,GAAGA,SAAS,GAAG,CAACjB,SAAS,CAACY,SAAS;EACrF,IAAI,CAACE,IAAI,GAAG,CAAC,CAACd,SAAS,CAACc,IAAI;EAC5B,IAAI,CAACC,IAAI,GAAGf,SAAS,CAACe,IAAI,KAAKE,SAAS,GAAG,EAAE,GAAGjB,SAAS,CAACe,IAAI,GAAG,EAAE;AACrE;AAEAX,eAAe,CAACY,SAAS,CAACE,QAAQ,GAAG,YAAW;EAC9C,OAAO,IAAI,CAACb,IAAI,GACV,IAAI,CAACC,KAAK,GACV,IAAI,CAACC,IAAI,GACT,IAAI,CAACC,MAAM,IACV,IAAI,CAACC,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,IACrB,IAAI,CAACC,KAAK,KAAKO,SAAS,GAAG,EAAE,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACV,KAAK,GAAG,CAAC,CAAC,CAAC,IAC5D,IAAI,CAACC,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,IACtB,IAAI,CAACC,SAAS,KAAKK,SAAS,GAAG,EAAE,GAAG,GAAG,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACR,SAAS,GAAG,CAAC,CAAC,CAAC,IAC1E,IAAI,CAACE,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,GACtB,IAAI,CAACC,IAAI;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}