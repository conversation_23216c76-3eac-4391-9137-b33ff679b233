{"ast": null, "code": "import { startOfISOWeek } from \"../../../startOfISOWeek.js\";\nimport { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigitsSigned } from \"../utils.js\";\n\n// ISO week-numbering year\nexport class ISOWeekYearParser extends Parser {\n  priority = 130;\n  parse(dateString, token) {\n    if (token === \"R\") {\n      return parseNDigitsSigned(4, dateString);\n    }\n    return parseNDigitsSigned(token.length, dateString);\n  }\n  set(date, _flags, value) {\n    const firstWeekOfYear = constructFrom(date, 0);\n    firstWeekOfYear.setFullYear(value, 0, 4);\n    firstWeekOfYear.setHours(0, 0, 0, 0);\n    return startOfISOWeek(firstWeekOfYear);\n  }\n  incompatibleTokens = [\"G\", \"y\", \"Y\", \"u\", \"Q\", \"q\", \"M\", \"L\", \"w\", \"d\", \"D\", \"e\", \"c\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["startOfISOWeek", "constructFrom", "<PERSON><PERSON><PERSON>", "parseNDigitsSigned", "ISOWeekYearParser", "priority", "parse", "dateString", "token", "length", "set", "date", "_flags", "value", "firstWeekOfYear", "setFullYear", "setHours", "incompatibleTokens"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/parse/_lib/parsers/ISOWeekYearParser.js"], "sourcesContent": ["import { startOfISOWeek } from \"../../../startOfISOWeek.js\";\nimport { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigitsSigned } from \"../utils.js\";\n\n// ISO week-numbering year\nexport class ISOWeekYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token) {\n    if (token === \"R\") {\n      return parseNDigitsSigned(4, dateString);\n    }\n\n    return parseNDigitsSigned(token.length, dateString);\n  }\n\n  set(date, _flags, value) {\n    const firstWeekOfYear = constructFrom(date, 0);\n    firstWeekOfYear.setFullYear(value, 0, 4);\n    firstWeekOfYear.setHours(0, 0, 0, 0);\n    return startOfISOWeek(firstWeekOfYear);\n  }\n\n  incompatibleTokens = [\n    \"G\",\n    \"y\",\n    \"Y\",\n    \"u\",\n    \"Q\",\n    \"q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"d\",\n    \"D\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,kBAAkB,QAAQ,aAAa;;AAEhD;AACA,OAAO,MAAMC,iBAAiB,SAASF,MAAM,CAAC;EAC5CG,QAAQ,GAAG,GAAG;EAEdC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,KAAK,GAAG,EAAE;MACjB,OAAOL,kBAAkB,CAAC,CAAC,EAAEI,UAAU,CAAC;IAC1C;IAEA,OAAOJ,kBAAkB,CAACK,KAAK,CAACC,MAAM,EAAEF,UAAU,CAAC;EACrD;EAEAG,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACvB,MAAMC,eAAe,GAAGb,aAAa,CAACU,IAAI,EAAE,CAAC,CAAC;IAC9CG,eAAe,CAACC,WAAW,CAACF,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IACxCC,eAAe,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACpC,OAAOhB,cAAc,CAACc,eAAe,CAAC;EACxC;EAEAG,kBAAkB,GAAG,CACnB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}