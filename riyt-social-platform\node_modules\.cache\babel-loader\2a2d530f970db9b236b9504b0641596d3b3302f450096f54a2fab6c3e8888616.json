{"ast": null, "code": "import MapCache from './_MapCache.js';\nimport setCacheAdd from './_setCacheAdd.js';\nimport setCacheHas from './_setCacheHas.js';\n\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\nfunction SetCache(values) {\n  var index = -1,\n    length = values == null ? 0 : values.length;\n  this.__data__ = new MapCache();\n  while (++index < length) {\n    this.add(values[index]);\n  }\n}\n\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\nexport default SetCache;", "map": {"version": 3, "names": ["MapCache", "setCacheAdd", "setCacheHas", "<PERSON><PERSON><PERSON>", "values", "index", "length", "__data__", "add", "prototype", "push", "has"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/lodash-es/_SetCache.js"], "sourcesContent": ["import MapCache from './_MapCache.js';\nimport setCacheAdd from './_setCacheAdd.js';\nimport setCacheHas from './_setCacheHas.js';\n\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\nfunction SetCache(values) {\n  var index = -1,\n      length = values == null ? 0 : values.length;\n\n  this.__data__ = new MapCache;\n  while (++index < length) {\n    this.add(values[index]);\n  }\n}\n\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\n\nexport default SetCache;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAE;EACxB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,MAAM,IAAI,IAAI,GAAG,CAAC,GAAGA,MAAM,CAACE,MAAM;EAE/C,IAAI,CAACC,QAAQ,GAAG,IAAIP,QAAQ,CAAD,CAAC;EAC5B,OAAO,EAAEK,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAI,CAACE,GAAG,CAACJ,MAAM,CAACC,KAAK,CAAC,CAAC;EACzB;AACF;;AAEA;AACAF,QAAQ,CAACM,SAAS,CAACD,GAAG,GAAGL,QAAQ,CAACM,SAAS,CAACC,IAAI,GAAGT,WAAW;AAC9DE,QAAQ,CAACM,SAAS,CAACE,GAAG,GAAGT,WAAW;AAEpC,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}