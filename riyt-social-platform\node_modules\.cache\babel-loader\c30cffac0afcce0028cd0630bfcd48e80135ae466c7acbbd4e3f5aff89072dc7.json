{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isBefore\n * @category Common Helpers\n * @summary Is the first date before the second one?\n *\n * @description\n * Is the first date before the second one?\n *\n * @param date - The date that should be before the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is before the second date\n *\n * @example\n * // Is 10 July 1989 before 11 February 1987?\n * const result = isBefore(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> false\n */\nexport function isBefore(date, dateToCompare) {\n  return +toDate(date) < +toDate(dateToCompare);\n}\n\n// Fallback for modularized imports:\nexport default isBefore;", "map": {"version": 3, "names": ["toDate", "isBefore", "date", "dateToCompare"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/isBefore.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name isBefore\n * @category Common Helpers\n * @summary Is the first date before the second one?\n *\n * @description\n * Is the first date before the second one?\n *\n * @param date - The date that should be before the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is before the second date\n *\n * @example\n * // Is 10 July 1989 before 11 February 1987?\n * const result = isBefore(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> false\n */\nexport function isBefore(date, dateToCompare) {\n  return +toDate(date) < +toDate(dateToCompare);\n}\n\n// Fallback for modularized imports:\nexport default isBefore;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,aAAa,EAAE;EAC5C,OAAO,CAACH,MAAM,CAACE,IAAI,CAAC,GAAG,CAACF,MAAM,CAACG,aAAa,CAAC;AAC/C;;AAEA;AACA,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}