{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useIsDateDisabled } from \"./useIsDateDisabled.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nimport { findClosestEnabledDate } from \"../internals/utils/date-utils.js\";\nimport { usePickerAdapter } from \"../hooks/usePickerAdapter.js\";\nconst createCalendarStateReducer = (reduceAnimations, adapter) => (state, action) => {\n  switch (action.type) {\n    case 'setVisibleDate':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.month,\n        isMonthSwitchingAnimating: !adapter.isSameMonth(action.month, state.currentMonth) && !reduceAnimations && !action.skipAnimation,\n        focusedDay: action.focusedDay\n      });\n    case 'changeMonthTimezone':\n      {\n        const newTimezone = action.newTimezone;\n        if (adapter.getTimezone(state.currentMonth) === newTimezone) {\n          return state;\n        }\n        let newCurrentMonth = adapter.setTimezone(state.currentMonth, newTimezone);\n        if (adapter.getMonth(newCurrentMonth) !== adapter.getMonth(state.currentMonth)) {\n          newCurrentMonth = adapter.setMonth(newCurrentMonth, adapter.getMonth(state.currentMonth));\n        }\n        return _extends({}, state, {\n          currentMonth: newCurrentMonth\n        });\n      }\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = params => {\n  const {\n    value,\n    referenceDate: referenceDateProp,\n    disableFuture,\n    disablePast,\n    maxDate,\n    minDate,\n    onMonthChange,\n    onYearChange,\n    reduceAnimations,\n    shouldDisableDate,\n    timezone,\n    getCurrentMonthFromVisibleDate\n  } = params;\n  const adapter = usePickerAdapter();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), adapter)).current;\n  const referenceDate = React.useMemo(() => {\n    return singleItemValueManager.getInitialReferenceValue({\n      value,\n      adapter,\n      timezone,\n      props: params,\n      referenceDate: referenceDateProp,\n      granularity: SECTION_TYPE_GRANULARITY.day\n    });\n  },\n  // We want the `referenceDate` to update on prop and `timezone` change (https://github.com/mui/mui-x/issues/10804)\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [referenceDateProp, timezone]);\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: referenceDate,\n    currentMonth: adapter.startOfMonth(referenceDate),\n    slideDirection: 'left'\n  });\n  const isDateDisabled = useIsDateDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast,\n    timezone\n  });\n\n  // Ensure that `calendarState.currentMonth` timezone is updated when `referenceDate` (or timezone changes)\n  // https://github.com/mui/mui-x/issues/10804\n  React.useEffect(() => {\n    dispatch({\n      type: 'changeMonthTimezone',\n      newTimezone: adapter.getTimezone(referenceDate)\n    });\n  }, [referenceDate, adapter]);\n  const setVisibleDate = useEventCallback(({\n    target,\n    reason\n  }) => {\n    if (reason === 'cell-interaction' && calendarState.focusedDay != null && adapter.isSameDay(target, calendarState.focusedDay)) {\n      return;\n    }\n    const skipAnimation = reason === 'cell-interaction';\n    let month;\n    let focusedDay;\n    if (reason === 'cell-interaction') {\n      month = getCurrentMonthFromVisibleDate(target, calendarState.currentMonth);\n      focusedDay = target;\n    } else {\n      month = adapter.isSameMonth(target, calendarState.currentMonth) ? calendarState.currentMonth : adapter.startOfMonth(target);\n      focusedDay = target;\n\n      // If the date is disabled, we try to find a non-disabled date inside the same month.\n      if (isDateDisabled(focusedDay)) {\n        const startOfMonth = adapter.startOfMonth(target);\n        const endOfMonth = adapter.endOfMonth(target);\n        focusedDay = findClosestEnabledDate({\n          adapter,\n          date: focusedDay,\n          minDate: adapter.isBefore(minDate, startOfMonth) ? startOfMonth : minDate,\n          maxDate: adapter.isAfter(maxDate, endOfMonth) ? endOfMonth : maxDate,\n          disablePast,\n          disableFuture,\n          isDateDisabled,\n          timezone\n        });\n      }\n    }\n    const hasChangedMonth = !adapter.isSameMonth(calendarState.currentMonth, month);\n    const hasChangedYear = !adapter.isSameYear(calendarState.currentMonth, month);\n    if (hasChangedMonth) {\n      onMonthChange?.(month);\n    }\n    if (hasChangedYear) {\n      onYearChange?.(adapter.startOfYear(month));\n    }\n    dispatch({\n      type: 'setVisibleDate',\n      month,\n      direction: adapter.isAfterDay(month, calendarState.currentMonth) ? 'left' : 'right',\n      focusedDay: calendarState.focusedDay != null && focusedDay != null && adapter.isSameDay(focusedDay, calendarState.focusedDay) ? calendarState.focusedDay : focusedDay,\n      skipAnimation\n    });\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  return {\n    referenceDate,\n    calendarState,\n    setVisibleDate,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useIsDateDisabled", "singleItemValueManager", "SECTION_TYPE_GRANULARITY", "findClosestEnabledDate", "usePickerAdapter", "createCalendarStateReducer", "reduceAnimations", "adapter", "state", "action", "type", "slideDirection", "direction", "currentMonth", "month", "isMonthSwitchingAnimating", "isSameMonth", "skipAnimation", "focusedDay", "newTimezone", "getTimezone", "newCurrentMonth", "setTimezone", "getMonth", "setMonth", "Error", "useCalendarState", "params", "value", "referenceDate", "referenceDateProp", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "onYearChange", "shouldDisableDate", "timezone", "getCurrentMonthFromVisibleDate", "reducerFn", "useRef", "Boolean", "current", "useMemo", "getInitialReferenceValue", "props", "granularity", "day", "calendarState", "dispatch", "useReducer", "startOfMonth", "isDateDisabled", "useEffect", "setVisibleDate", "target", "reason", "isSameDay", "endOfMonth", "date", "isBefore", "isAfter", "hasChangedMonth", "hasChangedYear", "isSameYear", "startOfYear", "isAfterDay", "onMonthSwitchingAnimationEnd", "useCallback"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/@mui/x-date-pickers/esm/DateCalendar/useCalendarState.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useIsDateDisabled } from \"./useIsDateDisabled.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nimport { findClosestEnabledDate } from \"../internals/utils/date-utils.js\";\nimport { usePickerAdapter } from \"../hooks/usePickerAdapter.js\";\nconst createCalendarStateReducer = (reduceAnimations, adapter) => (state, action) => {\n  switch (action.type) {\n    case 'setVisibleDate':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.month,\n        isMonthSwitchingAnimating: !adapter.isSameMonth(action.month, state.currentMonth) && !reduceAnimations && !action.skipAnimation,\n        focusedDay: action.focusedDay\n      });\n    case 'changeMonthTimezone':\n      {\n        const newTimezone = action.newTimezone;\n        if (adapter.getTimezone(state.currentMonth) === newTimezone) {\n          return state;\n        }\n        let newCurrentMonth = adapter.setTimezone(state.currentMonth, newTimezone);\n        if (adapter.getMonth(newCurrentMonth) !== adapter.getMonth(state.currentMonth)) {\n          newCurrentMonth = adapter.setMonth(newCurrentMonth, adapter.getMonth(state.currentMonth));\n        }\n        return _extends({}, state, {\n          currentMonth: newCurrentMonth\n        });\n      }\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = params => {\n  const {\n    value,\n    referenceDate: referenceDateProp,\n    disableFuture,\n    disablePast,\n    maxDate,\n    minDate,\n    onMonthChange,\n    onYearChange,\n    reduceAnimations,\n    shouldDisableDate,\n    timezone,\n    getCurrentMonthFromVisibleDate\n  } = params;\n  const adapter = usePickerAdapter();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), adapter)).current;\n  const referenceDate = React.useMemo(() => {\n    return singleItemValueManager.getInitialReferenceValue({\n      value,\n      adapter,\n      timezone,\n      props: params,\n      referenceDate: referenceDateProp,\n      granularity: SECTION_TYPE_GRANULARITY.day\n    });\n  },\n  // We want the `referenceDate` to update on prop and `timezone` change (https://github.com/mui/mui-x/issues/10804)\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [referenceDateProp, timezone]);\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: referenceDate,\n    currentMonth: adapter.startOfMonth(referenceDate),\n    slideDirection: 'left'\n  });\n  const isDateDisabled = useIsDateDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast,\n    timezone\n  });\n\n  // Ensure that `calendarState.currentMonth` timezone is updated when `referenceDate` (or timezone changes)\n  // https://github.com/mui/mui-x/issues/10804\n  React.useEffect(() => {\n    dispatch({\n      type: 'changeMonthTimezone',\n      newTimezone: adapter.getTimezone(referenceDate)\n    });\n  }, [referenceDate, adapter]);\n  const setVisibleDate = useEventCallback(({\n    target,\n    reason\n  }) => {\n    if (reason === 'cell-interaction' && calendarState.focusedDay != null && adapter.isSameDay(target, calendarState.focusedDay)) {\n      return;\n    }\n    const skipAnimation = reason === 'cell-interaction';\n    let month;\n    let focusedDay;\n    if (reason === 'cell-interaction') {\n      month = getCurrentMonthFromVisibleDate(target, calendarState.currentMonth);\n      focusedDay = target;\n    } else {\n      month = adapter.isSameMonth(target, calendarState.currentMonth) ? calendarState.currentMonth : adapter.startOfMonth(target);\n      focusedDay = target;\n\n      // If the date is disabled, we try to find a non-disabled date inside the same month.\n      if (isDateDisabled(focusedDay)) {\n        const startOfMonth = adapter.startOfMonth(target);\n        const endOfMonth = adapter.endOfMonth(target);\n        focusedDay = findClosestEnabledDate({\n          adapter,\n          date: focusedDay,\n          minDate: adapter.isBefore(minDate, startOfMonth) ? startOfMonth : minDate,\n          maxDate: adapter.isAfter(maxDate, endOfMonth) ? endOfMonth : maxDate,\n          disablePast,\n          disableFuture,\n          isDateDisabled,\n          timezone\n        });\n      }\n    }\n    const hasChangedMonth = !adapter.isSameMonth(calendarState.currentMonth, month);\n    const hasChangedYear = !adapter.isSameYear(calendarState.currentMonth, month);\n    if (hasChangedMonth) {\n      onMonthChange?.(month);\n    }\n    if (hasChangedYear) {\n      onYearChange?.(adapter.startOfYear(month));\n    }\n    dispatch({\n      type: 'setVisibleDate',\n      month,\n      direction: adapter.isAfterDay(month, calendarState.currentMonth) ? 'left' : 'right',\n      focusedDay: calendarState.focusedDay != null && focusedDay != null && adapter.isSameDay(focusedDay, calendarState.focusedDay) ? calendarState.focusedDay : focusedDay,\n      skipAnimation\n    });\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  return {\n    referenceDate,\n    calendarState,\n    setVisibleDate,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd\n  };\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,kCAAkC;AACzE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,MAAMC,0BAA0B,GAAGA,CAACC,gBAAgB,EAAEC,OAAO,KAAK,CAACC,KAAK,EAAEC,MAAM,KAAK;EACnF,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,gBAAgB;MACnB,OAAOb,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;QACzBG,cAAc,EAAEF,MAAM,CAACG,SAAS;QAChCC,YAAY,EAAEJ,MAAM,CAACK,KAAK;QAC1BC,yBAAyB,EAAE,CAACR,OAAO,CAACS,WAAW,CAACP,MAAM,CAACK,KAAK,EAAEN,KAAK,CAACK,YAAY,CAAC,IAAI,CAACP,gBAAgB,IAAI,CAACG,MAAM,CAACQ,aAAa;QAC/HC,UAAU,EAAET,MAAM,CAACS;MACrB,CAAC,CAAC;IACJ,KAAK,qBAAqB;MACxB;QACE,MAAMC,WAAW,GAAGV,MAAM,CAACU,WAAW;QACtC,IAAIZ,OAAO,CAACa,WAAW,CAACZ,KAAK,CAACK,YAAY,CAAC,KAAKM,WAAW,EAAE;UAC3D,OAAOX,KAAK;QACd;QACA,IAAIa,eAAe,GAAGd,OAAO,CAACe,WAAW,CAACd,KAAK,CAACK,YAAY,EAAEM,WAAW,CAAC;QAC1E,IAAIZ,OAAO,CAACgB,QAAQ,CAACF,eAAe,CAAC,KAAKd,OAAO,CAACgB,QAAQ,CAACf,KAAK,CAACK,YAAY,CAAC,EAAE;UAC9EQ,eAAe,GAAGd,OAAO,CAACiB,QAAQ,CAACH,eAAe,EAAEd,OAAO,CAACgB,QAAQ,CAACf,KAAK,CAACK,YAAY,CAAC,CAAC;QAC3F;QACA,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;UACzBK,YAAY,EAAEQ;QAChB,CAAC,CAAC;MACJ;IACF,KAAK,+BAA+B;MAClC,OAAOxB,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;QACzBO,yBAAyB,EAAE;MAC7B,CAAC,CAAC;IACJ;MACE,MAAM,IAAIU,KAAK,CAAC,iBAAiB,CAAC;EACtC;AACF,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGC,MAAM,IAAI;EACxC,MAAM;IACJC,KAAK;IACLC,aAAa,EAAEC,iBAAiB;IAChCC,aAAa;IACbC,WAAW;IACXC,OAAO;IACPC,OAAO;IACPC,aAAa;IACbC,YAAY;IACZ9B,gBAAgB;IAChB+B,iBAAiB;IACjBC,QAAQ;IACRC;EACF,CAAC,GAAGZ,MAAM;EACV,MAAMpB,OAAO,GAAGH,gBAAgB,CAAC,CAAC;EAClC,MAAMoC,SAAS,GAAG1C,KAAK,CAAC2C,MAAM,CAACpC,0BAA0B,CAACqC,OAAO,CAACpC,gBAAgB,CAAC,EAAEC,OAAO,CAAC,CAAC,CAACoC,OAAO;EACtG,MAAMd,aAAa,GAAG/B,KAAK,CAAC8C,OAAO,CAAC,MAAM;IACxC,OAAO3C,sBAAsB,CAAC4C,wBAAwB,CAAC;MACrDjB,KAAK;MACLrB,OAAO;MACP+B,QAAQ;MACRQ,KAAK,EAAEnB,MAAM;MACbE,aAAa,EAAEC,iBAAiB;MAChCiB,WAAW,EAAE7C,wBAAwB,CAAC8C;IACxC,CAAC,CAAC;EACJ,CAAC;EACD;EACA;EACA,CAAClB,iBAAiB,EAAEQ,QAAQ,CAAC,CAAC;EAC9B,MAAM,CAACW,aAAa,EAAEC,QAAQ,CAAC,GAAGpD,KAAK,CAACqD,UAAU,CAACX,SAAS,EAAE;IAC5DzB,yBAAyB,EAAE,KAAK;IAChCG,UAAU,EAAEW,aAAa;IACzBhB,YAAY,EAAEN,OAAO,CAAC6C,YAAY,CAACvB,aAAa,CAAC;IACjDlB,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM0C,cAAc,GAAGrD,iBAAiB,CAAC;IACvCqC,iBAAiB;IACjBH,OAAO;IACPD,OAAO;IACPF,aAAa;IACbC,WAAW;IACXM;EACF,CAAC,CAAC;;EAEF;EACA;EACAxC,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpBJ,QAAQ,CAAC;MACPxC,IAAI,EAAE,qBAAqB;MAC3BS,WAAW,EAAEZ,OAAO,CAACa,WAAW,CAACS,aAAa;IAChD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,aAAa,EAAEtB,OAAO,CAAC,CAAC;EAC5B,MAAMgD,cAAc,GAAGxD,gBAAgB,CAAC,CAAC;IACvCyD,MAAM;IACNC;EACF,CAAC,KAAK;IACJ,IAAIA,MAAM,KAAK,kBAAkB,IAAIR,aAAa,CAAC/B,UAAU,IAAI,IAAI,IAAIX,OAAO,CAACmD,SAAS,CAACF,MAAM,EAAEP,aAAa,CAAC/B,UAAU,CAAC,EAAE;MAC5H;IACF;IACA,MAAMD,aAAa,GAAGwC,MAAM,KAAK,kBAAkB;IACnD,IAAI3C,KAAK;IACT,IAAII,UAAU;IACd,IAAIuC,MAAM,KAAK,kBAAkB,EAAE;MACjC3C,KAAK,GAAGyB,8BAA8B,CAACiB,MAAM,EAAEP,aAAa,CAACpC,YAAY,CAAC;MAC1EK,UAAU,GAAGsC,MAAM;IACrB,CAAC,MAAM;MACL1C,KAAK,GAAGP,OAAO,CAACS,WAAW,CAACwC,MAAM,EAAEP,aAAa,CAACpC,YAAY,CAAC,GAAGoC,aAAa,CAACpC,YAAY,GAAGN,OAAO,CAAC6C,YAAY,CAACI,MAAM,CAAC;MAC3HtC,UAAU,GAAGsC,MAAM;;MAEnB;MACA,IAAIH,cAAc,CAACnC,UAAU,CAAC,EAAE;QAC9B,MAAMkC,YAAY,GAAG7C,OAAO,CAAC6C,YAAY,CAACI,MAAM,CAAC;QACjD,MAAMG,UAAU,GAAGpD,OAAO,CAACoD,UAAU,CAACH,MAAM,CAAC;QAC7CtC,UAAU,GAAGf,sBAAsB,CAAC;UAClCI,OAAO;UACPqD,IAAI,EAAE1C,UAAU;UAChBgB,OAAO,EAAE3B,OAAO,CAACsD,QAAQ,CAAC3B,OAAO,EAAEkB,YAAY,CAAC,GAAGA,YAAY,GAAGlB,OAAO;UACzED,OAAO,EAAE1B,OAAO,CAACuD,OAAO,CAAC7B,OAAO,EAAE0B,UAAU,CAAC,GAAGA,UAAU,GAAG1B,OAAO;UACpED,WAAW;UACXD,aAAa;UACbsB,cAAc;UACdf;QACF,CAAC,CAAC;MACJ;IACF;IACA,MAAMyB,eAAe,GAAG,CAACxD,OAAO,CAACS,WAAW,CAACiC,aAAa,CAACpC,YAAY,EAAEC,KAAK,CAAC;IAC/E,MAAMkD,cAAc,GAAG,CAACzD,OAAO,CAAC0D,UAAU,CAAChB,aAAa,CAACpC,YAAY,EAAEC,KAAK,CAAC;IAC7E,IAAIiD,eAAe,EAAE;MACnB5B,aAAa,GAAGrB,KAAK,CAAC;IACxB;IACA,IAAIkD,cAAc,EAAE;MAClB5B,YAAY,GAAG7B,OAAO,CAAC2D,WAAW,CAACpD,KAAK,CAAC,CAAC;IAC5C;IACAoC,QAAQ,CAAC;MACPxC,IAAI,EAAE,gBAAgB;MACtBI,KAAK;MACLF,SAAS,EAAEL,OAAO,CAAC4D,UAAU,CAACrD,KAAK,EAAEmC,aAAa,CAACpC,YAAY,CAAC,GAAG,MAAM,GAAG,OAAO;MACnFK,UAAU,EAAE+B,aAAa,CAAC/B,UAAU,IAAI,IAAI,IAAIA,UAAU,IAAI,IAAI,IAAIX,OAAO,CAACmD,SAAS,CAACxC,UAAU,EAAE+B,aAAa,CAAC/B,UAAU,CAAC,GAAG+B,aAAa,CAAC/B,UAAU,GAAGA,UAAU;MACrKD;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMmD,4BAA4B,GAAGtE,KAAK,CAACuE,WAAW,CAAC,MAAM;IAC3DnB,QAAQ,CAAC;MACPxC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,OAAO;IACLmB,aAAa;IACboB,aAAa;IACbM,cAAc;IACdF,cAAc;IACde;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}