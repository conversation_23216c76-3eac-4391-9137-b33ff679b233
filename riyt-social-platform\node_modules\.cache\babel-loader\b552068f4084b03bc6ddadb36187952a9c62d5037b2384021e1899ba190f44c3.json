{"ast": null, "code": "import { basis } from \"./basis.js\";\nexport default function (values) {\n  var n = values.length;\n  return function (t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n      v0 = values[(i + n - 1) % n],\n      v1 = values[i % n],\n      v2 = values[(i + 1) % n],\n      v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}", "map": {"version": 3, "names": ["basis", "values", "n", "length", "t", "i", "Math", "floor", "v0", "v1", "v2", "v3"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-interpolate/src/basisClosed.js"], "sourcesContent": ["import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,YAAY;AAEhC,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,CAAC,GAAGD,MAAM,CAACE,MAAM;EACrB,OAAO,UAASC,CAAC,EAAE;IACjB,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,CAACH,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAEA,CAAC,GAAGA,CAAC,IAAIF,CAAC,CAAC;MAC5CM,EAAE,GAAGP,MAAM,CAAC,CAACI,CAAC,GAAGH,CAAC,GAAG,CAAC,IAAIA,CAAC,CAAC;MAC5BO,EAAE,GAAGR,MAAM,CAACI,CAAC,GAAGH,CAAC,CAAC;MAClBQ,EAAE,GAAGT,MAAM,CAAC,CAACI,CAAC,GAAG,CAAC,IAAIH,CAAC,CAAC;MACxBS,EAAE,GAAGV,MAAM,CAAC,CAACI,CAAC,GAAG,CAAC,IAAIH,CAAC,CAAC;IAC5B,OAAOF,KAAK,CAAC,CAACI,CAAC,GAAGC,CAAC,GAAGH,CAAC,IAAIA,CAAC,EAAEM,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC/C,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}