import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
  Badge,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  Edit as EditorIcon,
  Visibility as ViewerIcon,
  Pending as PendingIcon,
  CheckCircle as ApprovedIcon,
  Cancel as RejectedIcon,
  Comment as CommentIcon,
  Notifications as NotificationIcon,
} from '@mui/icons-material';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  avatar?: string;
  status: 'active' | 'pending' | 'inactive';
  joinedAt: Date;
  lastActive: Date;
  permissions: string[];
}

interface ApprovalRequest {
  id: string;
  postId: string;
  content: string;
  requestedBy: string;
  requestedAt: Date;
  status: 'pending' | 'approved' | 'rejected';
  reviewedBy?: string;
  reviewedAt?: Date;
  comments: string;
}

const TeamPage: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [approvalRequests, setApprovalRequests] = useState<ApprovalRequest[]>([]);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [newMemberRole, setNewMemberRole] = useState<'admin' | 'editor' | 'viewer'>('editor');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Mock data for demo
  useEffect(() => {
    const mockTeamMembers: TeamMember[] = [
      {
        id: 'user_1',
        name: 'John Smith',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        joinedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),
        permissions: ['create', 'edit', 'delete', 'approve', 'manage_team'],
      },
      {
        id: 'user_2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'editor',
        status: 'active',
        joinedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
        lastActive: new Date(Date.now() - 30 * 60 * 1000),
        permissions: ['create', 'edit', 'request_approval'],
      },
      {
        id: 'user_3',
        name: 'Mike Davis',
        email: '<EMAIL>',
        role: 'viewer',
        status: 'active',
        joinedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        lastActive: new Date(Date.now() - 4 * 60 * 60 * 1000),
        permissions: ['view'],
      },
      {
        id: 'user_4',
        name: 'Lisa Wilson',
        email: '<EMAIL>',
        role: 'editor',
        status: 'pending',
        joinedAt: new Date(),
        lastActive: new Date(),
        permissions: ['create', 'edit', 'request_approval'],
      },
    ];

    const mockApprovalRequests: ApprovalRequest[] = [
      {
        id: 'approval_1',
        postId: 'post_1',
        content: 'Excited to announce our new product launch! 🚀 This revolutionary tool will change how you manage social media.',
        requestedBy: 'Sarah Johnson',
        requestedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        status: 'pending',
        comments: 'Ready for review - targeting Facebook and LinkedIn',
      },
      {
        id: 'approval_2',
        postId: 'post_2',
        content: 'Weekly team update: Amazing progress on our Q1 goals! 💪 Thanks to everyone for their hard work.',
        requestedBy: 'Mike Davis',
        requestedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        status: 'approved',
        reviewedBy: 'John Smith',
        reviewedAt: new Date(Date.now() - 20 * 60 * 60 * 1000),
        comments: 'Approved for posting on all platforms',
      },
    ];

    setTeamMembers(mockTeamMembers);
    setApprovalRequests(mockApprovalRequests);
  }, []);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <AdminIcon color="error" />;
      case 'editor':
        return <EditorIcon color="primary" />;
      case 'viewer':
        return <ViewerIcon color="action" />;
      default:
        return <PersonIcon />;
    }
  };

  const getRoleColor = (role: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'editor':
        return 'primary';
      case 'viewer':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'inactive':
        return 'default';
      default:
        return 'default';
    }
  };

  const handleInviteMember = async () => {
    if (!newMemberEmail.trim()) return;

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newMember: TeamMember = {
        id: `user_${Date.now()}`,
        name: newMemberEmail.split('@')[0],
        email: newMemberEmail,
        role: newMemberRole,
        status: 'pending',
        joinedAt: new Date(),
        lastActive: new Date(),
        permissions: newMemberRole === 'admin' 
          ? ['create', 'edit', 'delete', 'approve', 'manage_team']
          : newMemberRole === 'editor'
          ? ['create', 'edit', 'request_approval']
          : ['view'],
      };

      setTeamMembers(prev => [...prev, newMember]);
      setSuccess(`Invitation sent to ${newMemberEmail}`);
      setInviteDialogOpen(false);
      setNewMemberEmail('');
      setNewMemberRole('editor');
    } catch (err: any) {
      setError(`Failed to invite member: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleApprovalAction = async (requestId: string, action: 'approve' | 'reject') => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      setApprovalRequests(prev => 
        prev.map(request => 
          request.id === requestId 
            ? {
                ...request,
                status: action === 'approve' ? 'approved' : 'rejected',
                reviewedBy: 'John Smith', // Current user
                reviewedAt: new Date(),
              }
            : request
        )
      );

      setSuccess(`Post ${action}d successfully`);
    } catch (err: any) {
      setError(`Failed to ${action} post: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  const pendingApprovals = approvalRequests.filter(req => req.status === 'pending');

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Team Management
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage your team members, roles, and approval workflows.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Team Overview */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Team Overview
              </Typography>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Total Members
                </Typography>
                <Typography variant="h6">
                  {teamMembers.length}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Active Members
                </Typography>
                <Typography variant="h6">
                  {teamMembers.filter(m => m.status === 'active').length}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Pending Invites
                </Typography>
                <Typography variant="h6">
                  {teamMembers.filter(m => m.status === 'pending').length}
                </Typography>
              </Box>
              <Button
                variant="contained"
                fullWidth
                startIcon={<AddIcon />}
                onClick={() => setInviteDialogOpen(true)}
              >
                Invite Member
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Pending Approvals */}
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Pending Approvals
                </Typography>
                <Badge badgeContent={pendingApprovals.length} color="error">
                  <NotificationIcon />
                </Badge>
              </Box>
              
              {pendingApprovals.length === 0 ? (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={2}>
                  No pending approvals
                </Typography>
              ) : (
                <List>
                  {pendingApprovals.map((request) => (
                    <ListItem key={request.id} divider>
                      <ListItemText
                        primary={
                          <Typography variant="body1" sx={{ 
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                          }}>
                            {request.content}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              Requested by {request.requestedBy} • {formatTimeAgo(request.requestedAt)}
                            </Typography>
                            {request.comments && (
                              <Typography variant="body2" sx={{ mt: 0.5 }}>
                                💬 {request.comments}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box display="flex" gap={1}>
                          <Button
                            size="small"
                            color="error"
                            onClick={() => handleApprovalAction(request.id, 'reject')}
                            disabled={loading}
                          >
                            Reject
                          </Button>
                          <Button
                            size="small"
                            variant="contained"
                            color="success"
                            onClick={() => handleApprovalAction(request.id, 'approve')}
                            disabled={loading}
                          >
                            Approve
                          </Button>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Team Members */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Team Members
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Member</TableCell>
                      <TableCell>Role</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Joined</TableCell>
                      <TableCell>Last Active</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {teamMembers.map((member) => (
                      <TableRow key={member.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar src={member.avatar}>
                              {member.name.charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {member.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {member.email}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={member.role}
                            color={getRoleColor(member.role)}
                            size="small"
                            icon={getRoleIcon(member.role)}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={member.status}
                            color={getStatusColor(member.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {member.joinedAt.toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {formatTimeAgo(member.lastActive)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton size="small">
                            <MoreVertIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Invite Member Dialog */}
      <Dialog
        open={inviteDialogOpen}
        onClose={() => setInviteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Invite Team Member</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Email Address"
            type="email"
            value={newMemberEmail}
            onChange={(e) => setNewMemberEmail(e.target.value)}
            sx={{ mb: 2, mt: 1 }}
          />
          <FormControl fullWidth>
            <InputLabel>Role</InputLabel>
            <Select
              value={newMemberRole}
              onChange={(e) => setNewMemberRole(e.target.value as any)}
              label="Role"
            >
              <MenuItem value="viewer">Viewer - Can view content only</MenuItem>
              <MenuItem value="editor">Editor - Can create and edit content</MenuItem>
              <MenuItem value="admin">Admin - Full access including team management</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInviteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleInviteMember}
            variant="contained"
            disabled={loading || !newMemberEmail.trim()}
          >
            {loading ? 'Sending...' : 'Send Invitation'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TeamPage;
