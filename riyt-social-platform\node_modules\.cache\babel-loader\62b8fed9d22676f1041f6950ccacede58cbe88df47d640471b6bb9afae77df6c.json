{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nvar polarOptionsSlice = createSlice({\n  name: 'polarOptions',\n  initialState: null,\n  reducers: {\n    updatePolarOptions: (_state, action) => {\n      return action.payload;\n    }\n  }\n});\nexport var {\n  updatePolarOptions\n} = polarOptionsSlice.actions;\nexport var polarOptionsReducer = polarOptionsSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "polarOptionsSlice", "name", "initialState", "reducers", "updatePolarOptions", "_state", "action", "payload", "actions", "polarOptionsReducer", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/state/polarOptionsSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\nvar polarOptionsSlice = createSlice({\n  name: 'polarOptions',\n  initialState: null,\n  reducers: {\n    updatePolarOptions: (_state, action) => {\n      return action.payload;\n    }\n  }\n});\nexport var {\n  updatePolarOptions\n} = polarOptionsSlice.actions;\nexport var polarOptionsReducer = polarOptionsSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,IAAIC,iBAAiB,GAAGD,WAAW,CAAC;EAClCE,IAAI,EAAE,cAAc;EACpBC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE;IACRC,kBAAkB,EAAEA,CAACC,MAAM,EAAEC,MAAM,KAAK;MACtC,OAAOA,MAAM,CAACC,OAAO;IACvB;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTH;AACF,CAAC,GAAGJ,iBAAiB,CAACQ,OAAO;AAC7B,OAAO,IAAIC,mBAAmB,GAAGT,iBAAiB,CAACU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}