{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWednesday} function options.\n */\n\n/**\n * @name isWednesday\n * @category Weekday Helpers\n * @summary Is the given date Wednesday?\n *\n * @description\n * Is the given date Wednesday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Wednesday\n *\n * @example\n * // Is 24 September 2014 Wednesday?\n * const result = isWednesday(new Date(2014, 8, 24))\n * //=> true\n */\nexport function isWednesday(date, options) {\n  return toDate(date, options?.in).getDay() === 3;\n}\n\n// Fallback for modularized imports:\nexport default isWednesday;", "map": {"version": 3, "names": ["toDate", "isWednesday", "date", "options", "in", "getDay"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/isWednesday.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWednesday} function options.\n */\n\n/**\n * @name isWednesday\n * @category Weekday Helpers\n * @summary Is the given date Wednesday?\n *\n * @description\n * Is the given date Wednesday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Wednesday\n *\n * @example\n * // Is 24 September 2014 Wednesday?\n * const result = isWednesday(new Date(2014, 8, 24))\n * //=> true\n */\nexport function isWednesday(date, options) {\n  return toDate(date, options?.in).getDay() === 3;\n}\n\n// Fallback for modularized imports:\nexport default isWednesday;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC;AACjD;;AAEA;AACA,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}