{"ast": null, "code": "import EventEmitter from 'eventemitter3';\nvar eventCenter = new EventEmitter();\nexport { eventCenter };\nexport var TOOLTIP_SYNC_EVENT = 'recharts.syncEvent.tooltip';\nexport var BRUSH_SYNC_EVENT = 'recharts.syncEvent.brush';", "map": {"version": 3, "names": ["EventEmitter", "eventCenter", "TOOLTIP_SYNC_EVENT", "BRUSH_SYNC_EVENT"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/recharts/es6/util/Events.js"], "sourcesContent": ["import EventEmitter from 'eventemitter3';\nvar eventCenter = new EventEmitter();\nexport { eventCenter };\nexport var TOOLTIP_SYNC_EVENT = 'recharts.syncEvent.tooltip';\nexport var BRUSH_SYNC_EVENT = 'recharts.syncEvent.brush';"], "mappings": "AAAA,OAAOA,YAAY,MAAM,eAAe;AACxC,IAAIC,WAAW,GAAG,IAAID,YAAY,CAAC,CAAC;AACpC,SAASC,WAAW;AACpB,OAAO,IAAIC,kBAAkB,GAAG,4BAA4B;AAC5D,OAAO,IAAIC,gBAAgB,GAAG,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}