{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addDays} function options.\n */\n\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be added.\n * @param options - An object with options\n *\n * @returns The new date with the days added\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport function addDays(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n\n  // If 0 days, no-op to avoid changing times in the hour before end of DST\n  if (!amount) return _date;\n  _date.setDate(_date.getDate() + amount);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addDays;", "map": {"version": 3, "names": ["constructFrom", "toDate", "addDays", "date", "amount", "options", "_date", "in", "isNaN", "NaN", "setDate", "getDate"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/addDays.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addDays} function options.\n */\n\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be added.\n * @param options - An object with options\n *\n * @returns The new date with the days added\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport function addDays(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n\n  // If 0 days, no-op to avoid changing times in the hour before end of DST\n  if (!amount) return _date;\n\n  _date.setDate(_date.getDate() + amount);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addDays;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC7C,MAAMC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACvC,IAAIC,KAAK,CAACJ,MAAM,CAAC,EAAE,OAAOJ,aAAa,CAACK,OAAO,EAAEE,EAAE,IAAIJ,IAAI,EAAEM,GAAG,CAAC;;EAEjE;EACA,IAAI,CAACL,MAAM,EAAE,OAAOE,KAAK;EAEzBA,KAAK,CAACI,OAAO,CAACJ,KAAK,CAACK,OAAO,CAAC,CAAC,GAAGP,MAAM,CAAC;EACvC,OAAOE,KAAK;AACd;;AAEA;AACA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}