{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { setMonth } from \"./setMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link set} function options.\n */\n\n/**\n * @name set\n * @category Common Helpers\n * @summary Set date values to a given date.\n *\n * @description\n * Set date values to a given date.\n *\n * Sets time values to date from object `values`.\n * A value is not set if it is undefined or null or doesn't exist in `values`.\n *\n * Note about bundle size: `set` does not internally use `setX` functions from date-fns but instead opts\n * to use native `Date#setX` methods. If you use this function, you may not want to include the\n * other `setX` functions that date-fns provides if you are concerned about the bundle size.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param values - The date values to be set\n * @param options - The options\n *\n * @returns The new date with options set\n *\n * @example\n * // Transform 1 September 2014 into 20 October 2015 in a single line:\n * const result = set(new Date(2014, 8, 20), { year: 2015, month: 9, date: 20 })\n * //=> Tue Oct 20 2015 00:00:00\n *\n * @example\n * // Set 12 PM to 1 September 2014 01:23:45 to 1 September 2014 12:00:00:\n * const result = set(new Date(2014, 8, 1, 1, 23, 45), { hours: 12 })\n * //=> Mon Sep 01 2014 12:23:45\n */\nexport function set(date, values, options) {\n  let _date = toDate(date, options?.in);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(+_date)) return constructFrom(options?.in || date, NaN);\n  if (values.year != null) _date.setFullYear(values.year);\n  if (values.month != null) _date = setMonth(_date, values.month);\n  if (values.date != null) _date.setDate(values.date);\n  if (values.hours != null) _date.setHours(values.hours);\n  if (values.minutes != null) _date.setMinutes(values.minutes);\n  if (values.seconds != null) _date.setSeconds(values.seconds);\n  if (values.milliseconds != null) _date.setMilliseconds(values.milliseconds);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default set;", "map": {"version": 3, "names": ["constructFrom", "setMonth", "toDate", "set", "date", "values", "options", "_date", "in", "isNaN", "NaN", "year", "setFullYear", "month", "setDate", "hours", "setHours", "minutes", "setMinutes", "seconds", "setSeconds", "milliseconds", "setMilliseconds"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/set.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { setMonth } from \"./setMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link set} function options.\n */\n\n/**\n * @name set\n * @category Common Helpers\n * @summary Set date values to a given date.\n *\n * @description\n * Set date values to a given date.\n *\n * Sets time values to date from object `values`.\n * A value is not set if it is undefined or null or doesn't exist in `values`.\n *\n * Note about bundle size: `set` does not internally use `setX` functions from date-fns but instead opts\n * to use native `Date#setX` methods. If you use this function, you may not want to include the\n * other `setX` functions that date-fns provides if you are concerned about the bundle size.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param values - The date values to be set\n * @param options - The options\n *\n * @returns The new date with options set\n *\n * @example\n * // Transform 1 September 2014 into 20 October 2015 in a single line:\n * const result = set(new Date(2014, 8, 20), { year: 2015, month: 9, date: 20 })\n * //=> Tue Oct 20 2015 00:00:00\n *\n * @example\n * // Set 12 PM to 1 September 2014 01:23:45 to 1 September 2014 12:00:00:\n * const result = set(new Date(2014, 8, 1, 1, 23, 45), { hours: 12 })\n * //=> Mon Sep 01 2014 12:23:45\n */\nexport function set(date, values, options) {\n  let _date = toDate(date, options?.in);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(+_date)) return constructFrom(options?.in || date, NaN);\n\n  if (values.year != null) _date.setFullYear(values.year);\n  if (values.month != null) _date = setMonth(_date, values.month);\n  if (values.date != null) _date.setDate(values.date);\n  if (values.hours != null) _date.setHours(values.hours);\n  if (values.minutes != null) _date.setMinutes(values.minutes);\n  if (values.seconds != null) _date.setSeconds(values.seconds);\n  if (values.milliseconds != null) _date.setMilliseconds(values.milliseconds);\n\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default set;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACzC,IAAIC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;;EAErC;EACA,IAAIC,KAAK,CAAC,CAACF,KAAK,CAAC,EAAE,OAAOP,aAAa,CAACM,OAAO,EAAEE,EAAE,IAAIJ,IAAI,EAAEM,GAAG,CAAC;EAEjE,IAAIL,MAAM,CAACM,IAAI,IAAI,IAAI,EAAEJ,KAAK,CAACK,WAAW,CAACP,MAAM,CAACM,IAAI,CAAC;EACvD,IAAIN,MAAM,CAACQ,KAAK,IAAI,IAAI,EAAEN,KAAK,GAAGN,QAAQ,CAACM,KAAK,EAAEF,MAAM,CAACQ,KAAK,CAAC;EAC/D,IAAIR,MAAM,CAACD,IAAI,IAAI,IAAI,EAAEG,KAAK,CAACO,OAAO,CAACT,MAAM,CAACD,IAAI,CAAC;EACnD,IAAIC,MAAM,CAACU,KAAK,IAAI,IAAI,EAAER,KAAK,CAACS,QAAQ,CAACX,MAAM,CAACU,KAAK,CAAC;EACtD,IAAIV,MAAM,CAACY,OAAO,IAAI,IAAI,EAAEV,KAAK,CAACW,UAAU,CAACb,MAAM,CAACY,OAAO,CAAC;EAC5D,IAAIZ,MAAM,CAACc,OAAO,IAAI,IAAI,EAAEZ,KAAK,CAACa,UAAU,CAACf,MAAM,CAACc,OAAO,CAAC;EAC5D,IAAId,MAAM,CAACgB,YAAY,IAAI,IAAI,EAAEd,KAAK,CAACe,eAAe,CAACjB,MAAM,CAACgB,YAAY,CAAC;EAE3E,OAAOd,KAAK;AACd;;AAEA;AACA,eAAeJ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}