{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getYearCalendarUtilityClass(slot) {\n  return generateUtilityClass('MuiYearCalendar', slot);\n}\nexport const yearCalendarClasses = generateUtilityClasses('MuiYearCalendar', ['root', 'button', 'disabled', 'selected']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getYearCalendarUtilityClass", "slot", "yearCalendarClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/@mui/x-date-pickers/esm/YearCalendar/yearCalendarClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getYearCalendarUtilityClass(slot) {\n  return generateUtilityClass('MuiYearCalendar', slot);\n}\nexport const yearCalendarClasses = generateUtilityClasses('MuiYearCalendar', ['root', 'button', 'disabled', 'selected']);"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOH,oBAAoB,CAAC,iBAAiB,EAAEG,IAAI,CAAC;AACtD;AACA,OAAO,MAAMC,mBAAmB,GAAGH,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}