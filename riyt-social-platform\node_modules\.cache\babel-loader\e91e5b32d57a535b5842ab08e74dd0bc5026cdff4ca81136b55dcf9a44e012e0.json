{"ast": null, "code": "import overArg from './_overArg.js';\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\nexport default getPrototype;", "map": {"version": 3, "names": ["overArg", "getPrototype", "Object", "getPrototypeOf"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/lodash-es/_getPrototype.js"], "sourcesContent": ["import overArg from './_overArg.js';\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\n\nexport default getPrototype;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;;AAEnC;AACA,IAAIC,YAAY,GAAGD,OAAO,CAACE,MAAM,CAACC,cAAc,EAAED,MAAM,CAAC;AAEzD,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}