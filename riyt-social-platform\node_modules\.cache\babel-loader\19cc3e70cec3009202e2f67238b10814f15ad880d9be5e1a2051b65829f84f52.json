{"ast": null, "code": "import { secondsInHour } from \"./constants.js\";\n\n/**\n * @name hoursToSeconds\n * @category Conversion Helpers\n * @summary Convert hours to seconds.\n *\n * @description\n * Convert a number of hours to a full number of seconds.\n *\n * @param hours - The number of hours to be converted\n *\n * @returns The number of hours converted in seconds\n *\n * @example\n * // Convert 2 hours to seconds:\n * const result = hoursToSeconds(2)\n * //=> 7200\n */\nexport function hoursToSeconds(hours) {\n  return Math.trunc(hours * secondsInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToSeconds;", "map": {"version": 3, "names": ["secondsInHour", "hoursToSeconds", "hours", "Math", "trunc"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/hoursToSeconds.js"], "sourcesContent": ["import { secondsInHour } from \"./constants.js\";\n\n/**\n * @name hoursToSeconds\n * @category Conversion Helpers\n * @summary Convert hours to seconds.\n *\n * @description\n * Convert a number of hours to a full number of seconds.\n *\n * @param hours - The number of hours to be converted\n *\n * @returns The number of hours converted in seconds\n *\n * @example\n * // Convert 2 hours to seconds:\n * const result = hoursToSeconds(2)\n * //=> 7200\n */\nexport function hoursToSeconds(hours) {\n  return Math.trunc(hours * secondsInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToSeconds;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,gBAAgB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAGF,aAAa,CAAC;AAC1C;;AAEA;AACA,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}