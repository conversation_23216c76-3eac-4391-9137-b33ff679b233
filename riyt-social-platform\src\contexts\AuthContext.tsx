import React, { createContext, useContext, useEffect, useState } from 'react';
import { AuthState } from '../types';
import { authService } from '../services/firebase/auth';

interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (email: string, password: string, displayName?: string) => Promise<{ success: boolean; error?: string }>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<{ success: boolean; error?: string }>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
  sendEmailVerification: () => Promise<{ success: boolean; error?: string }>;
  updateProfile: (updates: { displayName?: string; photoURL?: string }) => Promise<{ success: boolean; error?: string }>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    const unsubscribe = authService.onAuthStateChanged((firebaseUser) => {
      if (firebaseUser) {
        const user = authService.getCurrentUser();
        setAuthState({
          user,
          loading: false,
          error: null,
        });
      } else {
        setAuthState({
          user: null,
          loading: false,
          error: null,
        });
      }
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    const result = await authService.signIn(email, password);
    
    if (result.success) {
      setAuthState(prev => ({ ...prev, loading: false }));
    } else {
      setAuthState(prev => ({ 
        ...prev, 
        loading: false, 
        error: result.error || 'Sign in failed' 
      }));
    }
    
    return result;
  };

  const register = async (email: string, password: string, displayName?: string) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    const result = await authService.register(email, password, displayName);
    
    if (result.success) {
      setAuthState(prev => ({ ...prev, loading: false }));
    } else {
      setAuthState(prev => ({ 
        ...prev, 
        loading: false, 
        error: result.error || 'Registration failed' 
      }));
    }
    
    return result;
  };

  const signInWithGoogle = async () => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    const result = await authService.signInWithGoogle();
    
    if (result.success) {
      setAuthState(prev => ({ ...prev, loading: false }));
    } else {
      setAuthState(prev => ({ 
        ...prev, 
        loading: false, 
        error: result.error || 'Google sign in failed' 
      }));
    }
    
    return result;
  };

  const signOut = async () => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    const result = await authService.signOut();
    
    setAuthState(prev => ({ ...prev, loading: false }));
    
    return result;
  };

  const resetPassword = async (email: string) => {
    setAuthState(prev => ({ ...prev, error: null }));
    return await authService.resetPassword(email);
  };

  const sendEmailVerification = async () => {
    setAuthState(prev => ({ ...prev, error: null }));
    return await authService.sendEmailVerification();
  };

  const updateProfile = async (updates: { displayName?: string; photoURL?: string }) => {
    setAuthState(prev => ({ ...prev, error: null }));
    
    const result = await authService.updateProfile(updates);
    
    if (result.success) {
      // Update local state
      const updatedUser = authService.getCurrentUser();
      setAuthState(prev => ({ ...prev, user: updatedUser }));
    }
    
    return result;
  };

  const clearError = () => {
    setAuthState(prev => ({ ...prev, error: null }));
  };

  const value: AuthContextType = {
    ...authState,
    signIn,
    register,
    signInWithGoogle,
    signOut,
    resetPassword,
    sendEmailVerification,
    updateProfile,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
