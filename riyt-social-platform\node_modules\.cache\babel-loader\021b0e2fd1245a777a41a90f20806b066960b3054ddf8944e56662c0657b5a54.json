{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport group, { rollup } from \"./group.js\";\nimport sort from \"./sort.js\";\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length !== 2 ? sort(rollup(values, reduce, key), ([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)) : sort(group(values, key), ([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))).map(([key]) => key);\n}", "map": {"version": 3, "names": ["ascending", "group", "rollup", "sort", "groupSort", "values", "reduce", "key", "length", "ak", "av", "bk", "bv", "map"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/d3-array/src/groupSort.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport group, {rollup} from \"./group.js\";\nimport sort from \"./sort.js\";\n\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length !== 2\n    ? sort(rollup(values, reduce, key), (([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)))\n    : sort(group(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))))\n    .map(([key]) => key);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,KAAK,IAAGC,MAAM,QAAO,YAAY;AACxC,OAAOC,IAAI,MAAM,WAAW;AAE5B,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAE;EACrD,OAAO,CAACD,MAAM,CAACE,MAAM,KAAK,CAAC,GACvBL,IAAI,CAACD,MAAM,CAACG,MAAM,EAAEC,MAAM,EAAEC,GAAG,CAAC,EAAG,CAAC,CAACE,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,KAAKZ,SAAS,CAACU,EAAE,EAAEE,EAAE,CAAC,IAAIZ,SAAS,CAACS,EAAE,EAAEE,EAAE,CAAE,CAAC,GACnGR,IAAI,CAACF,KAAK,CAACI,MAAM,EAAEE,GAAG,CAAC,EAAG,CAAC,CAACE,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,KAAKN,MAAM,CAACI,EAAE,EAAEE,EAAE,CAAC,IAAIZ,SAAS,CAACS,EAAE,EAAEE,EAAE,CAAE,CAAC,EACxFE,GAAG,CAAC,CAAC,CAACN,GAAG,CAAC,KAAKA,GAAG,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}