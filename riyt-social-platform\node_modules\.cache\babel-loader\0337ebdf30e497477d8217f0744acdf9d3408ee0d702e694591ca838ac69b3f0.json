{"ast": null, "code": "module.exports = require('../dist/compat/object/omit.js').omit;", "map": {"version": 3, "names": ["module", "exports", "require", "omit"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/es-toolkit/compat/omit.js"], "sourcesContent": ["module.exports = require('../dist/compat/object/omit.js').omit;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,+BAA+B,CAAC,CAACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}