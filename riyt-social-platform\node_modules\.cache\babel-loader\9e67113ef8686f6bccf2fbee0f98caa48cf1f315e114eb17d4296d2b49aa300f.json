{"ast": null, "code": "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatISO9075} function options.\n */\n\n/**\n * @name formatISO9075\n * @category Common Helpers\n * @summary Format the date according to the ISO 9075 standard (https://dev.mysql.com/doc/refman/5.7/en/date-and-time-functions.html#function_get-format).\n *\n * @description\n * Return the formatted date string in ISO 9075 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18 19:00:52'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075, short format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918 190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, date only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, time only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52'\n */\nexport function formatISO9075(date, options) {\n  const date_ = toDate(date, options?.in);\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n  const format = options?.format ?? \"extended\";\n  const representation = options?.representation ?? \"complete\";\n  let result = \"\";\n  const dateDelimiter = format === \"extended\" ? \"-\" : \"\";\n  const timeDelimiter = format === \"extended\" ? \":\" : \"\";\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== \"time\") {\n    const day = addLeadingZeros(date_.getDate(), 2);\n    const month = addLeadingZeros(date_.getMonth() + 1, 2);\n    const year = addLeadingZeros(date_.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = `${year}${dateDelimiter}${month}${dateDelimiter}${day}`;\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== \"date\") {\n    const hour = addLeadingZeros(date_.getHours(), 2);\n    const minute = addLeadingZeros(date_.getMinutes(), 2);\n    const second = addLeadingZeros(date_.getSeconds(), 2);\n\n    // If there's also date, separate it with time with a space\n    const separator = result === \"\" ? \"\" : \" \";\n\n    // HHmmss or HH:mm:ss.\n    result = `${result}${separator}${hour}${timeDelimiter}${minute}${timeDelimiter}${second}`;\n  }\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatISO9075;", "map": {"version": 3, "names": ["addLeadingZeros", "<PERSON><PERSON><PERSON><PERSON>", "toDate", "formatISO9075", "date", "options", "date_", "in", "RangeError", "format", "representation", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeDelimiter", "day", "getDate", "month", "getMonth", "year", "getFullYear", "hour", "getHours", "minute", "getMinutes", "second", "getSeconds", "separator"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/formatISO9075.js"], "sourcesContent": ["import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatISO9075} function options.\n */\n\n/**\n * @name formatISO9075\n * @category Common Helpers\n * @summary Format the date according to the ISO 9075 standard (https://dev.mysql.com/doc/refman/5.7/en/date-and-time-functions.html#function_get-format).\n *\n * @description\n * Return the formatted date string in ISO 9075 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18 19:00:52'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075, short format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918 190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, date only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, time only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52'\n */\nexport function formatISO9075(date, options) {\n  const date_ = toDate(date, options?.in);\n\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const format = options?.format ?? \"extended\";\n  const representation = options?.representation ?? \"complete\";\n\n  let result = \"\";\n\n  const dateDelimiter = format === \"extended\" ? \"-\" : \"\";\n  const timeDelimiter = format === \"extended\" ? \":\" : \"\";\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== \"time\") {\n    const day = addLeadingZeros(date_.getDate(), 2);\n    const month = addLeadingZeros(date_.getMonth() + 1, 2);\n    const year = addLeadingZeros(date_.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = `${year}${dateDelimiter}${month}${dateDelimiter}${day}`;\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== \"date\") {\n    const hour = addLeadingZeros(date_.getHours(), 2);\n    const minute = addLeadingZeros(date_.getMinutes(), 2);\n    const second = addLeadingZeros(date_.getSeconds(), 2);\n\n    // If there's also date, separate it with time with a space\n    const separator = result === \"\" ? \"\" : \" \";\n\n    // HHmmss or HH:mm:ss.\n    result = `${result}${separator}${hour}${timeDelimiter}${minute}${timeDelimiter}${second}`;\n  }\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatISO9075;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EAEvC,IAAI,CAACN,OAAO,CAACK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIE,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,MAAMC,MAAM,GAAGJ,OAAO,EAAEI,MAAM,IAAI,UAAU;EAC5C,MAAMC,cAAc,GAAGL,OAAO,EAAEK,cAAc,IAAI,UAAU;EAE5D,IAAIC,MAAM,GAAG,EAAE;EAEf,MAAMC,aAAa,GAAGH,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,EAAE;EACtD,MAAMI,aAAa,GAAGJ,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,EAAE;;EAEtD;EACA,IAAIC,cAAc,KAAK,MAAM,EAAE;IAC7B,MAAMI,GAAG,GAAGd,eAAe,CAACM,KAAK,CAACS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/C,MAAMC,KAAK,GAAGhB,eAAe,CAACM,KAAK,CAACW,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMC,IAAI,GAAGlB,eAAe,CAACM,KAAK,CAACa,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEpD;IACAR,MAAM,GAAG,GAAGO,IAAI,GAAGN,aAAa,GAAGI,KAAK,GAAGJ,aAAa,GAAGE,GAAG,EAAE;EAClE;;EAEA;EACA,IAAIJ,cAAc,KAAK,MAAM,EAAE;IAC7B,MAAMU,IAAI,GAAGpB,eAAe,CAACM,KAAK,CAACe,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACjD,MAAMC,MAAM,GAAGtB,eAAe,CAACM,KAAK,CAACiB,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;IACrD,MAAMC,MAAM,GAAGxB,eAAe,CAACM,KAAK,CAACmB,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;;IAErD;IACA,MAAMC,SAAS,GAAGf,MAAM,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG;;IAE1C;IACAA,MAAM,GAAG,GAAGA,MAAM,GAAGe,SAAS,GAAGN,IAAI,GAAGP,aAAa,GAAGS,MAAM,GAAGT,aAAa,GAAGW,MAAM,EAAE;EAC3F;EAEA,OAAOb,MAAM;AACf;;AAEA;AACA,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}