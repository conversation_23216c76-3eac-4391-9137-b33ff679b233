import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Menu,
  MenuItem,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  CalendarToday as CalendarIcon,
  AccessTime as AccessTimeIcon,
  Repeat as RepeatIcon,
} from '@mui/icons-material';
import { Post, PostStatus, SocialPlatform } from '../types';
import { schedulingService } from '../services/scheduling';

const ScheduledPostsPage: React.FC = () => {
  const [scheduledPosts, setScheduledPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  // Mock scheduled posts for demo
  useEffect(() => {
    const mockPosts: Post[] = [
      {
        id: 'post_1',
        content: {
          text: 'Excited to share our latest product update! 🚀 Check out the new features that will revolutionize your workflow.',
          images: [],
          link: 'https://example.com/product-update',
        },
        platforms: [SocialPlatform.FACEBOOK, SocialPlatform.TWITTER],
        status: PostStatus.SCHEDULED,
        scheduledTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        updatedAt: new Date(Date.now() - 30 * 60 * 1000),
        userId: 'user_1',
        isRecurring: false,
      },
      {
        id: 'post_2',
        content: {
          text: 'Weekly motivation Monday! 💪 Remember: Success is not final, failure is not fatal. It is the courage to continue that counts.',
          images: [],
        },
        platforms: [SocialPlatform.LINKEDIN],
        status: PostStatus.SCHEDULED,
        scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        userId: 'user_1',
        isRecurring: true,
        recurringPattern: 'Every Monday',
      },
      {
        id: 'post_3',
        content: {
          text: 'Behind the scenes at our office! 📸 Our team working hard to bring you amazing features.',
          images: ['https://example.com/image1.jpg'],
        },
        platforms: [SocialPlatform.INSTAGRAM, SocialPlatform.FACEBOOK],
        status: PostStatus.SCHEDULED,
        scheduledTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        createdAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
        updatedAt: new Date(Date.now() - 60 * 60 * 1000),
        userId: 'user_1',
        isRecurring: false,
      },
    ];
    setScheduledPosts(mockPosts);
  }, []);

  const getPlatformIcon = (platform: SocialPlatform) => {
    switch (platform) {
      case SocialPlatform.FACEBOOK:
        return <Facebook />;
      case SocialPlatform.TWITTER:
        return <Twitter />;
      case SocialPlatform.INSTAGRAM:
        return <Instagram />;
      case SocialPlatform.LINKEDIN:
        return <LinkedIn />;
      default:
        return null;
    }
  };

  const getPlatformColor = (platform: SocialPlatform): string => {
    switch (platform) {
      case SocialPlatform.FACEBOOK:
        return '#1877F2';
      case SocialPlatform.TWITTER:
        return '#1DA1F2';
      case SocialPlatform.INSTAGRAM:
        return '#E4405F';
      case SocialPlatform.LINKEDIN:
        return '#0A66C2';
      default:
        return '#666666';
    }
  };

  const getStatusColor = (status: PostStatus): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status) {
      case PostStatus.SCHEDULED:
        return 'info';
      case PostStatus.PUBLISHED:
        return 'success';
      case PostStatus.FAILED:
        return 'error';
      case PostStatus.DRAFT:
        return 'default';
      default:
        return 'default';
    }
  };

  const formatScheduledTime = (date: Date): string => {
    return schedulingService.formatScheduledTime(date, schedulingService.getUserTimezone());
  };

  const getTimeUntilPost = (scheduledTime: Date): string => {
    const now = new Date();
    const diff = scheduledTime.getTime() - now.getTime();
    
    if (diff < 0) return 'Overdue';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days} day${days > 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, post: Post) => {
    setAnchorEl(event.currentTarget);
    setSelectedPost(post);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPost(null);
  };

  const handleEditPost = () => {
    setEditDialogOpen(true);
    handleMenuClose();
  };

  const handleDeletePost = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handlePublishNow = async () => {
    if (!selectedPost) return;
    
    setLoading(true);
    try {
      // TODO: Implement immediate publishing
      setScheduledPosts(prev => 
        prev.map(post => 
          post.id === selectedPost.id 
            ? { ...post, status: PostStatus.PUBLISHED, publishedAt: new Date() }
            : post
        )
      );
      setSuccess('Post published successfully!');
    } catch (err: any) {
      setError(`Failed to publish post: ${err.message}`);
    } finally {
      setLoading(false);
      handleMenuClose();
    }
  };

  const handlePausePost = () => {
    if (!selectedPost) return;
    
    setScheduledPosts(prev => 
      prev.map(post => 
        post.id === selectedPost.id 
          ? { ...post, status: PostStatus.DRAFT }
          : post
      )
    );
    setSuccess('Post paused successfully!');
    handleMenuClose();
  };

  const confirmDeletePost = () => {
    if (!selectedPost) return;
    
    setScheduledPosts(prev => prev.filter(post => post.id !== selectedPost.id));
    setSuccess('Post deleted successfully!');
    setDeleteDialogOpen(false);
    setSelectedPost(null);
  };

  const sortedPosts = scheduledPosts
    .filter(post => post.status === PostStatus.SCHEDULED)
    .sort((a, b) => {
      if (!a.scheduledTime || !b.scheduledTime) return 0;
      return a.scheduledTime.getTime() - b.scheduledTime.getTime();
    });

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Scheduled Posts
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage your scheduled posts and publishing queue.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Stats Cards */}
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" component="div">
                    {sortedPosts.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Scheduled Posts
                  </Typography>
                </Box>
                <ScheduleIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" component="div">
                    {sortedPosts.filter(p => p.isRecurring).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Recurring Posts
                  </Typography>
                </Box>
                <RepeatIcon color="secondary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" component="div">
                    {sortedPosts.filter(p => p.scheduledTime && p.scheduledTime < new Date(Date.now() + 24 * 60 * 60 * 1000)).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Next 24 Hours
                  </Typography>
                </Box>
                <AccessTimeIcon color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" component="div">
                    {scheduledPosts.filter(p => p.status === PostStatus.PUBLISHED).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Published Today
                  </Typography>
                </Box>
                <CalendarIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Scheduled Posts Table */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Upcoming Posts
              </Typography>
              
              {sortedPosts.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <ScheduleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No Scheduled Posts
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Create your first scheduled post to see it here.
                  </Typography>
                  <Button variant="contained" href="/demo/create">
                    Create Post
                  </Button>
                </Box>
              ) : (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Content</TableCell>
                        <TableCell>Platforms</TableCell>
                        <TableCell>Scheduled Time</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {sortedPosts.map((post) => (
                        <TableRow key={post.id}>
                          <TableCell>
                            <Box>
                              <Typography variant="body2" sx={{ 
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                maxWidth: 300,
                              }}>
                                {post.content.text}
                              </Typography>
                              {post.isRecurring && (
                                <Chip 
                                  label={post.recurringPattern || 'Recurring'} 
                                  size="small" 
                                  color="secondary" 
                                  sx={{ mt: 1 }}
                                />
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box display="flex" gap={0.5}>
                              {post.platforms.map((platform) => (
                                <Tooltip key={platform} title={platform}>
                                  <Avatar 
                                    sx={{ 
                                      bgcolor: getPlatformColor(platform), 
                                      width: 24, 
                                      height: 24 
                                    }}
                                  >
                                    {getPlatformIcon(platform)}
                                  </Avatar>
                                </Tooltip>
                              ))}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography variant="body2">
                                {post.scheduledTime ? formatScheduledTime(post.scheduledTime) : 'Not scheduled'}
                              </Typography>
                              {post.scheduledTime && (
                                <Typography variant="caption" color="text.secondary">
                                  in {getTimeUntilPost(post.scheduledTime)}
                                </Typography>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip 
                              label={post.status} 
                              color={getStatusColor(post.status)} 
                              size="small" 
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              onClick={(e) => handleMenuOpen(e, post)}
                              size="small"
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handlePublishNow}>
          <PlayArrowIcon sx={{ mr: 1 }} />
          Publish Now
        </MenuItem>
        <MenuItem onClick={handlePausePost}>
          <PauseIcon sx={{ mr: 1 }} />
          Pause
        </MenuItem>
        <MenuItem onClick={handleEditPost}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDeletePost} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Scheduled Post</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this scheduled post? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={confirmDeletePost} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Dialog (placeholder) */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Edit Scheduled Post</DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            Post editing functionality will be implemented in the next update.
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Post Content"
            value={selectedPost?.content.text || ''}
            disabled
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>
            Close
          </Button>
          <Button variant="contained" disabled>
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ScheduledPostsPage;
