"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TimeRangePickerTabs = void 0;
var React = _interopRequireWildcard(require("react"));
var _clsx = _interopRequireDefault(require("clsx"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _Tab = _interopRequireDefault(require("@mui/material/Tab"));
var _Tabs = _interopRequireWildcard(require("@mui/material/Tabs"));
var _styles = require("@mui/material/styles");
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _icons = require("@mui/x-date-pickers/icons");
var _hooks = require("@mui/x-date-pickers/hooks");
var _timeRangePickerTabsClasses = require("./timeRangePickerTabsClasses");
var _hooks2 = require("../hooks");
var _jsxRuntime = require("react/jsx-runtime");
const useUtilityClasses = classes => {
  const slots = {
    root: ['root'],
    tab: ['tab']
  };
  return (0, _composeClasses.default)(slots, _timeRangePickerTabsClasses.getTimeRangePickerTabsUtilityClass, classes);
};
const TimeRangePickerTabsRoot = (0, _styles.styled)(_Tabs.default, {
  name: 'MuiTimeRangePickerTabs',
  slot: 'Root'
})(({
  theme
}) => ({
  boxShadow: `0 -1px 0 0 inset ${(theme.vars || theme).palette.divider}`,
  '&:last-child': {
    boxShadow: `0 1px 0 0 inset ${(theme.vars || theme).palette.divider}`,
    [`& .${_Tabs.tabsClasses.indicator}`]: {
      bottom: 'auto',
      top: 0
    }
  }
}));
const TimeRangePickerTab = (0, _styles.styled)(_Tab.default, {
  name: 'MuiTimeRangePickerTabs',
  slot: 'Tab'
})(({
  theme
}) => ({
  minHeight: '48px',
  gap: theme.spacing(1)
}));

/**
 * Demos:
 *
 * - [TimeRangePicker](https://mui.com/x/react-date-pickers/time-range-picker/)
 * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)
 *
 * API:
 *
 * - [TimeRangePickerTabs API](https://mui.com/x/api/date-pickers/time-range-picker-tabs/)
 */
const TimeRangePickerTabs = exports.TimeRangePickerTabs = function TimeRangePickerTabs(inProps) {
  const props = (0, _styles.useThemeProps)({
    props: inProps,
    name: 'MuiTimeRangePickerTabs'
  });
  const {
    timeIcon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_icons.TimeIcon, {}),
    hidden = typeof window === 'undefined' || window.innerHeight < 667,
    className,
    sx,
    classes: classesProp
  } = props;
  const translations = (0, _hooks.usePickerTranslations)();
  const {
    view,
    setView
  } = (0, _hooks.usePickerContext)();
  const {
    rangePosition,
    setRangePosition
  } = (0, _hooks2.usePickerRangePositionContext)();
  const classes = useUtilityClasses(classesProp);
  const handleChange = (event, value) => {
    if (rangePosition !== value) {
      setRangePosition(value);
    }
    if (view !== 'hours') {
      setView('hours');
    }
  };
  if (hidden) {
    return null;
  }
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(TimeRangePickerTabsRoot, {
    variant: "fullWidth",
    value: rangePosition,
    onChange: handleChange,
    className: (0, _clsx.default)(className, classes.root),
    sx: sx,
    children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(TimeRangePickerTab, {
      value: "start",
      iconPosition: "start",
      icon: timeIcon,
      label: translations.start,
      className: classes.tab
    }), /*#__PURE__*/(0, _jsxRuntime.jsx)(TimeRangePickerTab, {
      value: "end",
      iconPosition: "start",
      label: translations.end,
      icon: timeIcon,
      className: classes.tab
    })]
  });
};
if (process.env.NODE_ENV !== "production") TimeRangePickerTabs.displayName = "TimeRangePickerTabs";
process.env.NODE_ENV !== "production" ? TimeRangePickerTabs.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: _propTypes.default.object,
  className: _propTypes.default.string,
  /**
   * Toggles visibility of the tabs allowing view switching.
   * @default `window.innerHeight < 667` for `DesktopTimeRangePicker` and `MobileTimeRangePicker`, `displayStaticWrapperAs === 'desktop'` for `StaticTimeRangePicker`
   */
  hidden: _propTypes.default.bool,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),
  /**
   * Time tab icon.
   * @default Time
   */
  timeIcon: _propTypes.default.element
} : void 0;