"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateDateRange = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _validation = require("@mui/x-date-pickers/validation");
var _dateUtils = require("../internals/utils/date-utils");
var _valueManagers = require("../internals/utils/valueManagers");
const _excluded = ["shouldDisableDate"];
/**
 * Validation props used by the Date Range Picker, Date Range Field and Date Range Calendar components.
 */

/**
 * Validation props as received by the validateDateRange method.
 */

/**
 * Name of the props that should be defaulted before being passed to the validateDateRange method.
 */

const validateDateRange = ({
  adapter,
  value,
  timezone,
  props
}) => {
  const [start, end] = value;
  const {
      shouldDisableDate
    } = props,
    otherProps = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const dateValidations = [(0, _validation.validateDate)({
    adapter,
    value: start,
    timezone,
    props: (0, _extends2.default)({}, otherProps, {
      shouldDisableDate: day => !!shouldDisableDate?.(day, 'start')
    })
  }), (0, _validation.validateDate)({
    adapter,
    value: end,
    timezone,
    props: (0, _extends2.default)({}, otherProps, {
      shouldDisableDate: day => !!shouldDisableDate?.(day, 'end')
    })
  })];
  if (dateValidations[0] || dateValidations[1]) {
    return dateValidations;
  }

  // for partial input
  if (start === null || end === null) {
    return [null, null];
  }
  if (!(0, _dateUtils.isRangeValid)(adapter, value)) {
    return ['invalidRange', 'invalidRange'];
  }
  return [null, null];
};
exports.validateDateRange = validateDateRange;
validateDateRange.valueManager = _valueManagers.rangeValueManager;