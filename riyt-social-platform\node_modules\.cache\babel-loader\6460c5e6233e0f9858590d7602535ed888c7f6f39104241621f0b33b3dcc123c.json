{"ast": null, "code": "import { normalizeDates } from \"./normalizeDates.js\";\nexport function normalizeInterval(context, interval) {\n  const [start, end] = normalizeDates(context, interval.start, interval.end);\n  return {\n    start,\n    end\n  };\n}", "map": {"version": 3, "names": ["normalizeDates", "normalizeInterval", "context", "interval", "start", "end"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/date-fns/_lib/normalizeInterval.js"], "sourcesContent": ["import { normalizeDates } from \"./normalizeDates.js\";\n\nexport function normalizeInterval(context, interval) {\n  const [start, end] = normalizeDates(context, interval.start, interval.end);\n  return { start, end };\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AAEpD,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACnD,MAAM,CAACC,KAAK,EAAEC,GAAG,CAAC,GAAGL,cAAc,CAACE,OAAO,EAAEC,QAAQ,CAACC,KAAK,EAAED,QAAQ,CAACE,GAAG,CAAC;EAC1E,OAAO;IAAED,KAAK;IAAEC;EAAI,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}