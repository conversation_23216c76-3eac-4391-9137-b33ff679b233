import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControlLabel,
  Checkbox,
  Alert,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  IconButton,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Send as SendIcon,
  Schedule as ScheduleIcon,
  Image as ImageIcon,
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  ExpandMore as ExpandMoreIcon,
  AccessTime as AccessTimeIcon,
  Repeat as RepeatIcon,
  TrendingUp as TrendingUpIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import dayjs, { Dayjs } from 'dayjs';
import { SocialPlatform, SocialAccount, PostContent } from '../types';
import { socialMediaService } from '../services/social-apis';
import { schedulingService, ScheduleOptions, RecurringOptions } from '../services/scheduling';
import AIAssistant from '../components/AIAssistant/AIAssistant';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CreatePostPageEnhanced: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [postContent, setPostContent] = useState<PostContent>({
    text: '',
    images: [],
    link: '',
  });
  const [selectedPlatforms, setSelectedPlatforms] = useState<SocialPlatform[]>([]);
  const [connectedAccounts, setConnectedAccounts] = useState<SocialAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Scheduling state
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [scheduledTime, setScheduledTime] = useState<Dayjs | null>(null);
  const [timezone, setTimezone] = useState(schedulingService.getUserTimezone());
  const [isRecurring, setIsRecurring] = useState(false);
  const [recurringOptions, setRecurringOptions] = useState<RecurringOptions>({
    frequency: 'weekly',
    interval: 1,
    daysOfWeek: [],
  });

  // Mock connected accounts for demo
  useEffect(() => {
    const mockAccounts: SocialAccount[] = [
      {
        id: 'facebook_123',
        platform: SocialPlatform.FACEBOOK,
        username: 'My Business Page',
        displayName: 'My Business Page',
        profilePicture: undefined,
        isConnected: true,
        accessToken: 'mock_token',
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        connectedAt: new Date(),
        lastUsed: new Date(),
      },
      {
        id: 'twitter_456',
        platform: SocialPlatform.TWITTER,
        username: '@mybusiness',
        displayName: 'My Business',
        profilePicture: undefined,
        isConnected: true,
        accessToken: 'mock_token',
        refreshToken: 'mock_refresh',
        expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000),
        connectedAt: new Date(),
        lastUsed: new Date(),
      },
    ];
    setConnectedAccounts(mockAccounts);
  }, []);

  const getPlatformIcon = (platform: SocialPlatform) => {
    switch (platform) {
      case SocialPlatform.FACEBOOK:
        return <Facebook />;
      case SocialPlatform.TWITTER:
        return <Twitter />;
      case SocialPlatform.INSTAGRAM:
        return <Instagram />;
      case SocialPlatform.LINKEDIN:
        return <LinkedIn />;
      default:
        return null;
    }
  };

  const getPlatformColor = (platform: SocialPlatform): string => {
    const info = socialMediaService.getPlatformInfo(platform);
    return info.color;
  };

  const getCharacterCount = (platform: SocialPlatform): { current: number; limit: number; remaining: number } => {
    const limit = socialMediaService.getCharacterLimit(platform);
    const current = postContent.text.length;
    return {
      current,
      limit,
      remaining: limit - current,
    };
  };

  const handlePlatformToggle = (platform: SocialPlatform) => {
    setSelectedPlatforms(prev => 
      prev.includes(platform)
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    );
  };

  const validatePost = (): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!postContent.text.trim()) {
      errors.push('Post content cannot be empty');
    }

    if (selectedPlatforms.length === 0) {
      errors.push('Please select at least one platform');
    }

    // Validate content for each selected platform
    selectedPlatforms.forEach(platform => {
      const validation = socialMediaService.validateContent(platform, postContent);
      if (!validation.valid) {
        errors.push(...validation.errors);
      }
    });

    return { valid: errors.length === 0, errors };
  };

  const handlePublishNow = async () => {
    const validation = validatePost();
    if (!validation.valid) {
      setError(validation.errors.join('. '));
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const results = [];
      
      for (const platform of selectedPlatforms) {
        const account = connectedAccounts.find(acc => acc.platform === platform);
        if (account) {
          const result = await socialMediaService.postContent(account, postContent);
          results.push({ platform, ...result });
        }
      }

      const successfulPosts = results.filter(r => r.success);
      const failedPosts = results.filter(r => !r.success);

      if (successfulPosts.length > 0) {
        setSuccess(`Successfully posted to ${successfulPosts.map(p => p.platform).join(', ')}`);
      }

      if (failedPosts.length > 0) {
        setError(`Failed to post to ${failedPosts.map(p => p.platform).join(', ')}`);
      }

      // Clear form if all posts were successful
      if (failedPosts.length === 0) {
        setPostContent({ text: '', images: [], link: '' });
        setSelectedPlatforms([]);
      }

    } catch (err: any) {
      setError(`Failed to publish post: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSchedulePost = () => {
    setScheduleDialogOpen(true);
  };

  const handleScheduleConfirm = () => {
    if (!scheduledTime) {
      setError('Please select a scheduled time');
      return;
    }

    const validation = validatePost();
    if (!validation.valid) {
      setError(validation.errors.join('. '));
      return;
    }

    const scheduleValidation = schedulingService.validateScheduleTime(
      scheduledTime.toDate(),
      timezone
    );

    if (!scheduleValidation.valid) {
      setError(scheduleValidation.error || 'Invalid schedule time');
      return;
    }

    // TODO: Save scheduled post to database
    setSuccess(`Post scheduled for ${schedulingService.formatScheduledTime(scheduledTime.toDate(), timezone)}`);
    setScheduleDialogOpen(false);
    
    // Clear form
    setPostContent({ text: '', images: [], link: '' });
    setSelectedPlatforms([]);
    setScheduledTime(null);
  };

  const getOptimalTimes = () => {
    return schedulingService.getOptimalTimesForPlatforms(selectedPlatforms, timezone);
  };

  const suggestOptimalTime = () => {
    const suggested = schedulingService.suggestNextOptimalTime(selectedPlatforms, timezone);
    if (suggested) {
      setScheduledTime(dayjs(suggested));
    }
  };

  const timezones = schedulingService.getTimezones();

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          Create Post
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Create and publish content across your connected social media platforms.
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Post Content */}
          <Grid size={{ xs: 12, md: 8 }}>
            <Card>
              <CardContent>
                <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)} sx={{ mb: 2 }}>
                  <Tab label="Compose" />
                  <Tab label="Preview" />
                  <Tab label="AI Assistant" />
                  <Tab label="Analytics" />
                </Tabs>

                <TabPanel value={tabValue} index={0}>
                  <TextField
                    fullWidth
                    multiline
                    rows={6}
                    placeholder="What's on your mind?"
                    value={postContent.text}
                    onChange={(e) => setPostContent(prev => ({ ...prev, text: e.target.value }))}
                    sx={{ mb: 2 }}
                  />

                  <TextField
                    fullWidth
                    label="Link (optional)"
                    placeholder="https://example.com"
                    value={postContent.link}
                    onChange={(e) => setPostContent(prev => ({ ...prev, link: e.target.value }))}
                    sx={{ mb: 2 }}
                  />

                  <Button
                    variant="outlined"
                    startIcon={<ImageIcon />}
                    sx={{ mb: 2 }}
                    disabled
                  >
                    Add Images (Coming Soon)
                  </Button>
                </TabPanel>

                <TabPanel value={tabValue} index={1}>
                  <Typography variant="h6" gutterBottom>
                    Platform Previews
                  </Typography>
                  {selectedPlatforms.map(platform => (
                    <Paper key={platform} sx={{ p: 2, mb: 2 }}>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Avatar sx={{ bgcolor: getPlatformColor(platform), width: 24, height: 24 }}>
                          {getPlatformIcon(platform)}
                        </Avatar>
                        <Typography variant="subtitle2">{platform}</Typography>
                      </Box>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                        {postContent.text || 'Your post content will appear here...'}
                      </Typography>
                      {postContent.link && (
                        <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                          {postContent.link}
                        </Typography>
                      )}
                    </Paper>
                  ))}
                </TabPanel>

                <TabPanel value={tabValue} index={2}>
                  <AIAssistant
                    currentContent={postContent.text}
                    selectedPlatforms={selectedPlatforms}
                    onContentSuggestion={(content) => setPostContent(prev => ({ ...prev, text: content }))}
                    onHashtagSuggestion={(hashtags) => {
                      const hashtagText = hashtags.join(' ');
                      setPostContent(prev => ({
                        ...prev,
                        text: prev.text + (prev.text ? ' ' : '') + hashtagText
                      }));
                    }}
                  />
                </TabPanel>

                <TabPanel value={tabValue} index={3}>
                  <Typography variant="h6" gutterBottom>
                    Posting Recommendations
                  </Typography>
                  {selectedPlatforms.map(platform => {
                    const recommendations = schedulingService.getPostingFrequencyRecommendations(platform);
                    return (
                      <Paper key={platform} sx={{ p: 2, mb: 2 }}>
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Avatar sx={{ bgcolor: getPlatformColor(platform), width: 24, height: 24 }}>
                            {getPlatformIcon(platform)}
                          </Avatar>
                          <Typography variant="subtitle2">{platform}</Typography>
                        </Box>
                        <Typography variant="body2">
                          Recommended: {recommendations.recommended} posts per {recommendations.unit}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {recommendations.description}
                        </Typography>
                      </Paper>
                    );
                  })}
                </TabPanel>

                {/* Character counts for selected platforms */}
                {selectedPlatforms.length > 0 && (
                  <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Character Count
                    </Typography>
                    {selectedPlatforms.map(platform => {
                      const count = getCharacterCount(platform);
                      const isOverLimit = count.remaining < 0;
                      
                      return (
                        <Box key={platform} display="flex" alignItems="center" gap={1} mb={1}>
                          <Avatar sx={{ bgcolor: getPlatformColor(platform), width: 24, height: 24 }}>
                            {getPlatformIcon(platform)}
                          </Avatar>
                          <Typography variant="body2">
                            {platform}:
                          </Typography>
                          <Typography 
                            variant="body2" 
                            color={isOverLimit ? 'error' : 'text.secondary'}
                          >
                            {count.current}/{count.limit}
                          </Typography>
                          {isOverLimit && (
                            <Chip 
                              label={`${Math.abs(count.remaining)} over limit`} 
                              color="error" 
                              size="small" 
                            />
                          )}
                        </Box>
                      );
                    })}
                  </Paper>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Platform Selection and Actions */}
          <Grid size={{ xs: 12, md: 4 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Select Platforms
                </Typography>
                
                {connectedAccounts.length === 0 ? (
                  <Alert severity="info">
                    No connected accounts. <Button href="/demo/accounts">Connect accounts</Button>
                  </Alert>
                ) : (
                  <List>
                    {connectedAccounts.map((account) => (
                      <ListItem key={account.id} dense>
                        <ListItemAvatar>
                          <Avatar 
                            sx={{ bgcolor: getPlatformColor(account.platform), width: 32, height: 32 }}
                            src={account.profilePicture}
                          >
                            {getPlatformIcon(account.platform)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={account.displayName}
                          secondary={account.platform}
                        />
                        <ListItemSecondaryAction>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={selectedPlatforms.includes(account.platform)}
                                onChange={() => handlePlatformToggle(account.platform)}
                              />
                            }
                            label=""
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                )}
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Publish Options
                </Typography>
                
                <Box display="flex" flexDirection="column" gap={2}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<SendIcon />}
                    onClick={handlePublishNow}
                    disabled={loading || selectedPlatforms.length === 0 || !postContent.text.trim()}
                  >
                    {loading ? 'Publishing...' : 'Publish Now'}
                  </Button>
                  
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<ScheduleIcon />}
                    onClick={handleSchedulePost}
                    disabled={loading || selectedPlatforms.length === 0 || !postContent.text.trim()}
                  >
                    Schedule Post
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Schedule Dialog */}
        <Dialog
          open={scheduleDialogOpen}
          onClose={() => setScheduleDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" alignItems="center" gap={1}>
              <ScheduleIcon />
              Schedule Post
            </Box>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <DateTimePicker
                    label="Scheduled Time"
                    value={scheduledTime}
                    onChange={setScheduledTime}
                    minDateTime={dayjs().add(5, 'minute')}
                    maxDateTime={dayjs().add(1, 'year')}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        helperText: 'Minimum 5 minutes from now'
                      }
                    }}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <FormControl fullWidth>
                    <InputLabel>Timezone</InputLabel>
                    <Select
                      value={timezone}
                      onChange={(e) => setTimezone(e.target.value)}
                      label="Timezone"
                    >
                      {timezones.map((tz) => (
                        <MenuItem key={tz.value} value={tz.value}>
                          {tz.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              {/* Optimal Time Suggestions */}
              {selectedPlatforms.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Box display="flex" alignItems="center" gap={1} mb={2}>
                    <TrendingUpIcon color="primary" />
                    <Typography variant="h6">Optimal Posting Times</Typography>
                    <Tooltip title="Based on platform engagement data">
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                  
                  <Button
                    variant="outlined"
                    onClick={suggestOptimalTime}
                    startIcon={<AccessTimeIcon />}
                    sx={{ mb: 2 }}
                  >
                    Use Next Optimal Time
                  </Button>

                  <Box>
                    {getOptimalTimes().slice(0, 3).map((time, index) => (
                      <Chip
                        key={index}
                        label={time.description}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Recurring Options */}
              <Accordion sx={{ mt: 2 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <RepeatIcon />
                    <Typography>Recurring Post</Typography>
                    <Switch
                      checked={isRecurring}
                      onChange={(e) => setIsRecurring(e.target.checked)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  {isRecurring && (
                    <Grid container spacing={2}>
                      <Grid xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel>Frequency</InputLabel>
                          <Select
                            value={recurringOptions.frequency}
                            onChange={(e) => setRecurringOptions(prev => ({ 
                              ...prev, 
                              frequency: e.target.value as any 
                            }))}
                            label="Frequency"
                          >
                            <MenuItem value="daily">Daily</MenuItem>
                            <MenuItem value="weekly">Weekly</MenuItem>
                            <MenuItem value="monthly">Monthly</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Interval"
                          type="number"
                          value={recurringOptions.interval}
                          onChange={(e) => setRecurringOptions(prev => ({ 
                            ...prev, 
                            interval: parseInt(e.target.value) || 1 
                          }))}
                          inputProps={{ min: 1, max: 30 }}
                          helperText={`Every ${recurringOptions.interval} ${recurringOptions.frequency.slice(0, -2)}(s)`}
                        />
                      </Grid>
                    </Grid>
                  )}
                </AccordionDetails>
              </Accordion>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setScheduleDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleScheduleConfirm}
              variant="contained"
              disabled={!scheduledTime}
            >
              Schedule Post
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default CreatePostPageEnhanced;
