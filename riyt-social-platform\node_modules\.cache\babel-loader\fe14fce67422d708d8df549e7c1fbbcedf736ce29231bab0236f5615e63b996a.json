{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst unset = require('./unset.js');\nconst cloneDeep = require('../../object/cloneDeep.js');\nfunction omit(obj, ...keysArr) {\n  if (obj == null) {\n    return {};\n  }\n  const result = cloneDeep.cloneDeep(obj);\n  for (let i = 0; i < keysArr.length; i++) {\n    let keys = keysArr[i];\n    switch (typeof keys) {\n      case 'object':\n        {\n          if (!Array.isArray(keys)) {\n            keys = Array.from(keys);\n          }\n          for (let j = 0; j < keys.length; j++) {\n            const key = keys[j];\n            unset.unset(result, key);\n          }\n          break;\n        }\n      case 'string':\n      case 'symbol':\n      case 'number':\n        {\n          unset.unset(result, keys);\n          break;\n        }\n    }\n  }\n  return result;\n}\nexports.omit = omit;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "unset", "require", "cloneDeep", "omit", "obj", "keysArr", "result", "i", "length", "keys", "Array", "isArray", "from", "j", "key"], "sources": ["C:/Users/<USER>/Documents/augment-projects/ryt/riyt-social-platform/node_modules/es-toolkit/dist/compat/object/omit.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst unset = require('./unset.js');\nconst cloneDeep = require('../../object/cloneDeep.js');\n\nfunction omit(obj, ...keysArr) {\n    if (obj == null) {\n        return {};\n    }\n    const result = cloneDeep.cloneDeep(obj);\n    for (let i = 0; i < keysArr.length; i++) {\n        let keys = keysArr[i];\n        switch (typeof keys) {\n            case 'object': {\n                if (!Array.isArray(keys)) {\n                    keys = Array.from(keys);\n                }\n                for (let j = 0; j < keys.length; j++) {\n                    const key = keys[j];\n                    unset.unset(result, key);\n                }\n                break;\n            }\n            case 'string':\n            case 'symbol':\n            case 'number': {\n                unset.unset(result, keys);\n                break;\n            }\n        }\n    }\n    return result;\n}\n\nexports.omit = omit;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACnC,MAAMC,SAAS,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AAEtD,SAASE,IAAIA,CAACC,GAAG,EAAE,GAAGC,OAAO,EAAE;EAC3B,IAAID,GAAG,IAAI,IAAI,EAAE;IACb,OAAO,CAAC,CAAC;EACb;EACA,MAAME,MAAM,GAAGJ,SAAS,CAACA,SAAS,CAACE,GAAG,CAAC;EACvC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,IAAI,GAAGJ,OAAO,CAACE,CAAC,CAAC;IACrB,QAAQ,OAAOE,IAAI;MACf,KAAK,QAAQ;QAAE;UACX,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;YACtBA,IAAI,GAAGC,KAAK,CAACE,IAAI,CAACH,IAAI,CAAC;UAC3B;UACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACD,MAAM,EAAEK,CAAC,EAAE,EAAE;YAClC,MAAMC,GAAG,GAAGL,IAAI,CAACI,CAAC,CAAC;YACnBb,KAAK,CAACA,KAAK,CAACM,MAAM,EAAEQ,GAAG,CAAC;UAC5B;UACA;QACJ;MACA,KAAK,QAAQ;MACb,KAAK,QAAQ;MACb,KAAK,QAAQ;QAAE;UACXd,KAAK,CAACA,KAAK,CAACM,MAAM,EAAEG,IAAI,CAAC;UACzB;QACJ;IACJ;EACJ;EACA,OAAOH,MAAM;AACjB;AAEAV,OAAO,CAACO,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}